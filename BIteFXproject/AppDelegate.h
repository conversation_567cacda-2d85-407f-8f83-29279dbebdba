//
//  AppDelegate.h
//  BIteFXproject
//
//  Created by IndiaNIC_08 on 13/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "Reachability.h"
#import "NetworkReachability.h"
#import "ICloud.h"
#include <sys/types.h>
#include <sys/sysctl.h>

@class BiteFXMainScreenVC;

@interface AppDelegate : UIResponder <UIApplicationDelegate,UIAlertViewDelegate,iCloudDocProtocol> {
    
    NSMutableArray *mutArrPlayVideo;
    
    
    NSMutableArray *arrlastCollectionRow;

    NSMutableArray *arrScrollViewCount;

    
    
    int intSelectedScrNo;
    
    int intlastResizeViewYAxis;
    
    int intLastScrollIndicatorHeight;
    
    int intTabViewIndexPath;
    
    BOOL boolSelectedPlaylist;
    
    BOOL isFirstScrollView;
    BOOL isUpdatedMVIfile;
    BOOL isRegistrationSuccess;
    BOOL isFullversionDownloading;
    BOOL isUpdateDownloading;
    BOOL isDownloadCancelled;
    BOOL isFullZipcompleted;
    BOOL isUpdateZipcompleted;
    BOOL isUnregisteredDone;
    BOOL wasDownloadinProgress;
    NSString *strDeviceId;
    BOOL isChangedinPlayList;
//    int     intUpdatesdownloadCompleted;
    
    NSMutableArray *mutArrDataItem;
    
    BOOL isiCloudAvailble;
    
    int intStatusCheckForiCloud;
    
    UIActivityIndicatorView *actView;
    
}
@property () NSInteger  intSelectedTab;
@property () NSInteger  intPrevSelectedTab;
@property () NSInteger selectedModeSize;
@property (strong, nonatomic) UIWindow *window;

@property (strong, nonatomic) BiteFXMainScreenVC *viewController;

@property (strong, nonatomic) NSMutableArray *mutArrPlayVideo;

@property (nonatomic,strong) NSMutableArray *arrlastCollectionRow;

@property (nonatomic,strong)  NSMutableArray *arrScrollViewCount;

@property (nonatomic, strong) NSMutableArray *arrUserDefineSeq;


@property (nonatomic, strong) NSMutableArray *arrMovie;
@property (nonatomic, strong) NSMutableArray *arrImage;
@property (nonatomic, strong) NSMutableArray *arrPresentation;

@property (nonatomic, strong) NSMutableArray *arrMovieFavourite;
@property (nonatomic, strong) NSMutableArray *arrImageFavourite;
@property (nonatomic, strong) NSMutableArray *arrPresentationFavourite;

@property (nonatomic, strong) NSMutableArray *arrMovieCopy;
@property (nonatomic, strong) NSMutableArray *arrImageCopy;
@property (nonatomic, strong) NSMutableArray *arrPresentationCopy;

@property (nonatomic, strong) NSMutableArray *arrSelectedMovie;
@property (nonatomic, strong) NSMutableArray *arrSelectedImage;

@property (nonatomic, strong) NSMutableArray *arrSearchMovie;
@property (nonatomic, strong) NSMutableArray *arrSearchImage;
@property (nonatomic, strong) NSMutableArray *arrSearchPresentation;
@property (nonatomic, strong) NSMutableArray *arrLockImage;

@property (nonatomic, copy) NSString *selectedObj;
@property (nonatomic, strong) NSMutableArray *arrMedia;

@property (nonatomic) NSInteger intSelectedVideo;

@property (nonatomic) int intSelectedScrNo;

@property (nonatomic) int  intlastResizeViewYAxis;
@property (nonatomic) int  intlastPlayVideoState;

@property (nonatomic) int  intLastScrollIndicatorHeight;
@property (nonatomic) int intTabViewIndexPath;

@property (nonatomic) BOOL boolSelectedPlaylist;
@property (nonatomic) BOOL isFirstScrollView;

@property (nonatomic) BOOL isDownloadCancelled;
@property (nonatomic,assign)BOOL isUpdatedMVIfile;
@property (nonatomic,assign)BOOL isUnregisteredDone;


@property (nonatomic,assign)BOOL isRegistrationSuccess;
@property (nonatomic)BOOL wasDownloadinProgress;
@property (nonatomic,assign) BOOL isFullversionDownloading;

@property (nonatomic) BOOL isUpdateDownloading;
@property (nonatomic) BOOL isFullZipcompleted;

@property (nonatomic) BOOL isUpdateZipcompleted;
@property (nonatomic,assign)BOOL isChangedinPlayList;
@property (nonatomic,strong)NSString *strDeviceId;
@property (nonatomic, strong)NSMutableArray *mutArrDataItem;
@property (strong) UIButton *btnHideUnhideImages;
@property (nonatomic) BOOL isiCloudAvailble;

@property (nonatomic) int intStatusCheckForiCloud;

@property (nonatomic, assign) BOOL isSkipVideoFullVerison;
@property (nonatomic, assign) BOOL isSkipVideoUpdateVerison;
@property (nonatomic, assign) BOOL isDownloadManually;

@property (nonatomic, assign) BOOL isPresentationInfoOpen;
@property (nonatomic, assign) BOOL isReOpenPresentationInfo;

// This is used to show selected image, video and presentation from FullScreenMode to SelectionPanel...
@property (nonatomic, assign) BOOL isScrollToSelectedIndex;

// Version 2.5 change support for app upgrade with updates list...
@property (nonatomic, assign) BOOL isUpgradeAvailable;

@property (nonatomic, assign) BOOL isFavourite;

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.
// This is used to store current selected FeatureSet...
@property (nonatomic, assign) NSInteger intSelectedFeatureId;

@property (NS_NONATOMIC_IOSONLY, readonly) BOOL connected;

- (void)awakeFromNib;

// Skip back of attributes

- (BOOL)addSkipBackupAttributeToItemAtURL:(NSURL *)URL;
@property (NS_NONATOMIC_IOSONLY, readonly) BOOL checkIsFullversionDownloading;

+(NSArray*) print_free_memory;

+(NSString *) BitFXFull;
+(NSString*)UpdatePath;

+(NSString *) documentPath;

+(NSMutableArray *) listOfRemainFileDownload;

+ (void)createFoldersInDocumentDirectoryToSaveAppData;

- (void)iCloudIntegrationforDB;

- (void)checkForRestore;

//- (void)showAlertView;

- (void)startIndicator;
- (void)startPurchaseIndicatorWithID:(NSInteger)tag;

- (void)stopIndicator;

- (void)createTableFoNewVersion;

- (void) createTableForUserDefineSqu;

- (void)callTestwebServiceRemainingVideos;
@property (NS_NONATOMIC_IOSONLY, getter=isNetWorkAvailable, readonly) BOOL netWorkAvailable;


@property (NS_NONATOMIC_IOSONLY, readonly, copy) NSString *platform;
- (void) copyFile;
- (void) copyUpdateFile;
-(void)overWriteDB;
-(void)hideShowAllImportedImages:(NSString*)srtVal;
@property (NS_NONATOMIC_IOSONLY, getter=getObjectFrame, readonly) CGSize objectFrame;
@property (NS_NONATOMIC_IOSONLY, getter=getMaxCountForRow, readonly) int maxCountForRow;
@end
