//
//  AnimationView.h
//  BIteFXproject
//
//  Created by IndiaNIC Infotech Ltd on 19/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol AnimationViewDelegate;

@interface AnimationView : UIViewController
{
    IBOutlet UILabel *lblName;
    IBOutlet UIButton *btnVid;
    
    id <AnimationViewDelegate> animDelegate;
}

@property (nonatomic, strong) IBOutlet UILabel *lblName;
@property (nonatomic, strong) IBOutlet UIButton *btnVid;
@property (nonatomic, strong) IBOutlet UIImageView *imgLeftCorner;

@property (strong, nonatomic) IBOutlet UIView *viewLeftCorner;
@property (strong, nonatomic) IBOutlet UIButton *btnLeftCorner;
@property (strong, nonatomic) IBOutlet UIButton *btnCopyUDpresentation;

@property (nonatomic, strong) IBOutlet UILabel *lblTitle;
@property (nonatomic, strong) IBOutlet UIButton *btnShowHideImgs;
@property (strong, nonatomic) IBOutlet UIButton *btnCopySequence;
@property (strong, nonatomic) IBOutlet UIButton *btnInfoSequence;
@property (strong, nonatomic) IBOutlet UIButton *btnFavSequence;
@property (strong, nonatomic) IBOutlet UIButton *btnFav;
@property (strong, nonatomic) IBOutlet UIButton *btnFavMedium;
@property (strong, nonatomic) IBOutlet UIButton *btnFavLarge;

@property (nonatomic, strong) id <AnimationViewDelegate> animDelegate;

#pragma mark --------------------------------
#pragma mark Version 2.4 Changes

@property (nonatomic, strong) IBOutlet UIButton *btnDelete;

- (IBAction)onClickPlay:(id)sender;

- (IBAction)onClickSelect:(id)sender;

@end


@protocol AnimationViewDelegate <NSObject>

- (void)playVideo:(id)sender;
- (void)favImgVideo:(UIButton*)sender;

@optional
- (void)selectVideo:(id)sender;


@end
