//
//  BiteFXMainScreenVC.m
//  BIteFXproject
//
//  Created by IndiaNIC_08 on 13/12/11.
//  Copyright (c) 2011 __MyCompanyName__. All rights reserved.

//Live
#define InApp_One_Month_Product_ID @"com.bitefx.bitefx.1month_autorenew"

// SCM:2004-03-04 This option is no longer offered and will be removed from the code (per <PERSON>). We only offer montly autorenewal subscriptions.
#define Str_ItemInApp1 @"com.bitefx.bitefx.1year_autorenew"

// SCM:2004-03-04 This option is no longer offered and will be removed from the code (per <PERSON>). We only offer montly autorenewal subscriptions.
//#define Str_ItemInApp1 @"com.bitefx.bitefx.1YearPackage"

// SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
#define Str_ItemInApp2 @"com.bitefx.bitefx.6MonthPackage"

//TestServer WE SHOULD NEVER USE THESE!!! These two non-autorenw subscription versions were required initially because Apple would not
//                                        allow us to use autorenew plans. That ban was lifted and now we only offer autorenew monthly.
//                                        (Note:I am leaving these commented out #defines for clarity when actually removing obsolete 6/12
//                                        month non-autorenewing subscriptions)
//#define Str_ItemInApp1 @"com.bitefx.bitefxTestServer.1YearPackage"
//#define Str_ItemInApp2 @"com.bitefx.bitefxTestServer.6MonthPackage"

#define SYSTEM_VERSION_GREATER_THAN(v)              ([[[UIDevice currentDevice] systemVersion] compare:v options:NSNumericSearch] == NSOrderedDescending)
#define SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(v)  ([[[UIDevice currentDevice] systemVersion] compare:v options:NSNumericSearch] != NSOrderedAscending)

//Start by tarun...

#define strAlertTitle @"BiteFX"
#define definenoInternetConnection @"No internet connection found. Please try again!"
#define defineFileDownloadSuccessMessage @"All files downloaded successfully"
#define defineDownloadingUpdates @"Downloading updates..."
#define defineEnterEmailAddress @"Please enter email-address"
#define defineEnterValidEmailAddress @"Please enter valid email-address"
#define defineEnterSericalNumber @"Please enter Serial number"
#define strAlertCancelTitle @"OK"
#define strAlertContinueTitle @"Continue"
#define strAlertRenewSubscriptionTitle @"Renew subscription"
#define strAlertBiteFXSupport @"No response from BiteFX server. Please contact BiteFX Support (email and numbers available at BiteFX.com)"
// Since strAlertBiteFXSupport is used 8 places in the code, the following error codes will be appended to it to identify where the error was triggered.
#define CHECK_SUBSCRIPTION_ERROR 4001
#define IN_APP_VERIFICATION_ERROR 4002
#define CHECK_UPDATE_IN_APP_ERROR 4003
#define DEACTIVATE_USER_ERROR 4004
#define REGISTER_USER_ERROR 4005
#define CHECK_ELIGIBILITY_ERROR 4006
// These three "BAD_RESPONSE" errors occur when an invalid response dictionary is passed into the respective calls.
#define CHECK_UPDATE_IN_APP_BAD_RESPONSE_ERROR 4007
#define IN_APP_VERIFICATION_BAD_RESPONSE_ERROR 4008
#define CHECK_SUBSCRIPTION_BAD_RESPONSE_ERROR 4009
#define BFX_UNKNOWN -1 // Used when server return code is unavailable.

#define UserDefaults [NSUserDefaults standardUserDefaults]
#define SharedDatabase [Database sharedDatabase]
#define selectAllDataFromUserTable @"select * from UserMaster"

#define defineRedColor [UIColor redColor]
#define defineGrayColor [UIColor grayColor]
#define defineBlackColor [UIColor blackColor]
#define defineWhiteColor [UIColor whiteColor]
#define btnHideImage @"btnHide.png"
#define btnUnHideImage @"btnUnHide.png"
//end by tarun...
#define DOCUMENT_DIRECTORY_PATH [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0]

//ipad.bitefx.com
#import "BiteFXMainScreenVC.h"
#import "AnimationPanelVC.h"
#import "Database.h"
#import "ErrorFormatter.h"
#import "XMLParser.h"
#import "XMLParserUpdate.h"
#import "XMLParserFeature.h"
#import "PlayerView.h"
#import "ICloud.h"
#import "Reachability.h"
#import "UpdateCell.h"
#import "UITableViewCell+NIB.h"
#import "skipCell.h"
#import "WebService.h"

@implementation BiteFXMainScreenVC
@synthesize m_playerView,m_queueplayer,videoUrlArr,strSerialNumber;
@synthesize mutArrUpdateDownload;
@synthesize sliderSpeedControl;
@synthesize MutArrUpList;
@synthesize ArrUpdatedownload;
@synthesize mutArrFullVersion;
@synthesize arrPlayerItems,playerVideoplay;
@synthesize strEmail; // User Login email will stored in this.
@synthesize txtFieldEmail;
@synthesize arrMviFile;
@synthesize mutArrTotalmviFiles;
@synthesize aStrMviFile;
@synthesize strFullPath;
@synthesize downloadManager;
@synthesize mutArrSkipVideoList;
@synthesize mutArrSkipDownloadList;

// Helper method to find the current first responder in a view hierarchy
- (UIResponder *)findFirstResponderInView:(UIView *)view {
    if (view.isFirstResponder) {
        return view;
    }
    for (UIView *subview in view.subviews) {
        UIResponder *responder = [self findFirstResponderInView:subview];
        if (responder) return responder;
    }
    return nil;
}

- (void)keyboardWillShow:(NSNotification *)notification {
    // Do not process if txtSearch is first responder
    UIResponder *firstResponder = [self findFirstResponderInView:self.view];
    if ([firstResponder isKindOfClass:[UITextField class]]) {
        UITextField *activeField = (UITextField *)firstResponder;
        NSLog(@"[DEBUG] First responder: %@, identifier: %@, placeholder: %@", NSStringFromClass([activeField class]), [activeField accessibilityIdentifier], activeField.placeholder);        
        if ([activeField respondsToSelector:@selector(accessibilityIdentifier)] && [[activeField accessibilityIdentifier] isEqualToString:@"txtSearch"]) {
            return;
        }
        // Alternatively, if you can check by class or placeholder, adjust here
        if ([NSStringFromClass([activeField class]) isEqualToString:@"SearchBarTextField"] || [activeField.placeholder isEqualToString:@"Search"]) {
            return;
        }
    }
    NSDictionary *info = [notification userInfo];
    CGRect keyboardFrame = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    keyboardFrame = [self.view convertRect:keyboardFrame fromView:nil];
    CGSize keyboardSize = keyboardFrame.size;
    
    // Calculate keyboard animation duration and curve
    CGFloat duration = [info[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [info[UIKeyboardAnimationCurveUserInfoKey] intValue];
    
    // Convert text field frame to self.view coordinates
    CGRect textFieldFrame = [txtFieldSerialnumber1.superview convertRect:txtFieldSerialnumber1.frame toView:self.view];
    
    // Check if keyboard overlaps txtFieldEmail
    BOOL isOverlapping = CGRectIntersectsRect(textFieldFrame, keyboardFrame);
    NSLog(isOverlapping ? @"Keyboard is overlapping the txtFieldEmail!" : @"Keyboard is NOT overlapping the txtFieldEmail.");
    
    // Existing logic for offset calculation
    CGFloat screenHeight = [[UIScreen mainScreen] bounds].size.height;
    CGFloat textFieldBottom = textFieldFrame.origin.y + textFieldFrame.size.height + 50;
    CGFloat keyboardTop = screenHeight - keyboardSize.height;
    
    // Calculate offset needed
    CGFloat offset = 0;
    if (textFieldBottom > keyboardTop) {
        offset = textFieldBottom - keyboardTop + 50;
        
        [UIView animateWithDuration:duration
                              delay:0
                            options:(curve << 16)
                         animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(0, -offset);
        } completion:nil];
    }
}

- (void)keyboardWillHide:(NSNotification *)notification {
    NSDictionary *info = [notification userInfo];
    CGFloat duration = [info[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [info[UIKeyboardAnimationCurveUserInfoKey] intValue];
    
    [UIView animateWithDuration:duration
                          delay:0
                        options:(curve << 16)
                     animations:^{
        self.view.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        // Custom initialization
    }
    return self;
}
- (void)didReceiveMemoryWarning {
    // Releases the view if it doesn't have a superview.
    [super didReceiveMemoryWarning];
    NSLog(@"didReceiveMemoryWarning");
}
#pragma mark - View lifecycle
- (void)viewDidLoad
{
    [super viewDidLoad];
    
    // Register for keyboard notifications
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
                                               
    wbView = [[InfoWebView alloc]initWithFrame:CGRectMake(0, 0, 324, 695)];
    btnRestore.enabled = YES;
    loopBtn.selected = YES;
    inAppPurchase = [[InAppPurchase alloc] init];
    inAppPurchase.delegate = self;
    lblAppVersion.text = [NSBundle mainBundle].infoDictionary[(NSString *)kCFBundleVersionKey];//@"CFBundleShortVersionString"];
    isUpdateInterrupted = 0;
    
    btnSkip.titleLabel.textAlignment = NSTextAlignmentCenter;
    
    self.downloadManager = [[DownloadManager alloc] initWithDelegate:self];
    self.downloadManager.maxConcurrentDownloads = 1;
    
    [[NSNotificationCenter defaultCenter] addObserver: self selector: @selector(reachabilityChanged:) name: kReachabilityChangedNotification object: nil];
    
    //Change the host name here to change the server your monitoring
    //    hostReach = [[Reachability reachabilityWithHostName: @"http://test_ipad.bitefx.com"] retain];
    hostReach = [Reachability reachabilityWithHostName: WebserviceURLuserTesting];
    [hostReach startNotifier];
    [self updateInterfaceWithReachability: hostReach];
    
    internetReach = [Reachability reachabilityForInternetConnection];
    [internetReach startNotifier];
    [self updateInterfaceWithReachability: internetReach];
    
    wifiReach = [Reachability reachabilityForLocalWiFi];
    [wifiReach startNotifier];
    [self updateInterfaceWithReachability: wifiReach];
    
    self.downloadManager.isDownloadFailed = 0;
    
    [self checkForNewVersion];
    
    intupdate = 0;
    intVersiontoCheck = 0;
    appDelegate.wasDownloadinProgress = FALSE;
    imgMenuUpdates.hidden = YES;
    isMviDownloading = FALSE;
    intMviCounter = 0;
    UpdateCompleted = FALSE;
    intnumberOfMviFiles = 0;
    intParsingCounter = 1;
    
    isFullVersionXML = FALSE;
    //For checking updates
    
    isUpdatesChecked = FALSE;
    //This function will check whether any updates available on server or not.
    
    // CR Changes
    str1MonthPrice = @"";
    str6MonthsPrice = @"";
    str1YearPrice = @"";
    
    switch ([UserDefaults  integerForKey:@"UserStatusSubscription"]) {
        case 1:
            userSubStat = InAppSubscribed;
            break;
        case 2:
            userSubStat = Subscribed;
            break;
        default:
            userSubStat = NotSubscribed;
            break;
    }
    
    if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
        
        if (userSubStat == Subscribed) {
            [self callUpdateCheckWebservice:nil];
        } else if (userSubStat == InAppSubscribed) {
            [self callUpdateCheckForInAppWebservice:nil];
        }
        imgUpdatesAvailable.hidden = NO;
        btnUpdate.enabled = YES;
        btnUpdate.titleLabel.textColor = defineBlackColor;
        
    } else {
        imgUpdatesAvailable.hidden = YES;
        btnUpdate.enabled = NO;
        btnUpdate.titleLabel.textColor = defineGrayColor;
        btnSkip.enabled = NO;
        btnSkip.titleLabel.textColor = defineGrayColor;
    }
    isHtmlDownload = FALSE;
    isJpgDownoad = FALSE;
    intVideoNumberCounter = 0;
    isUpdateparsing2Done = FALSE;
    intHtmlFileCounter = 0;
    intJpgFileCounter = 0;
    imgUpdatesAvailable.hidden = YES;
    downloadingmviStarted = FALSE;
    
    arrMviFile =            [[NSMutableArray alloc]init];
    mutArrTotalmviFiles =   [[NSMutableArray alloc]init];
    
    isSlidertouchedDuringPlaying = FALSE;
    success = FALSE;
    UPDAtesComplted = FALSE;
    FullorUpdate = 0;
    isAlreadyDownloaded = FALSE;
    intUpdateCounter =0;
    inthtmlUpdateCounter=0;
    intJpgUpdateCounter=0;
    
    //To checking fulldownloaded or unregister or updates downloaded;
    
    intFullversiondownloadCompleted = 0;
    intUpdatesdownloadCompleted = 0;
    intUnregisterDone = 0;
    
    MutArrUpList = [[NSMutableArray alloc]init];
    self.ArrUpdatedownload = [NSMutableArray arrayWithCapacity:0];
    
    [UserDefaults removeObjectForKey:@"speed"];
    [UserDefaults removeObjectForKey:@"frames"];
    
    isPlayerfinished = FALSE;
    currentFrame = 1;
    floatCount = 0.0;
    Speed  = 1.0;
    
    //actiVityIndicator for download
    activityIndiForDownload = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
    
    activityIndiForDownload.frame = CGRectMake(447, 550, 37, 37);
    activityIndiForDownload.hidden = YES;
    
    timerFrameCounter = [[NSTimer alloc]init];
    arrPlayerItems = [[NSMutableArray alloc]init];
    m_queueplayer = [[AVQueuePlayer alloc]init];
    
    intCount = 0;
    floatSpeedMultiplier = 1.0;
    intcountofVideo = 0;
    currentIndex = 0;
    
    isnextClicked = FALSE;
    isPreviousClicked = FALSE;
    isanimationFinished = FALSE;
    isPlaying=FALSE;
    
    [self setPlayPauseImageForNormalState];
    
    isRegistered=FALSE;
    
    btnHelpView.hidden=YES;
    btnHelpClose.hidden = YES;
    
    btnHelpView.frame = CGRectMake(0, 0, 1024, 768);
    btnHelpClose.frame = CGRectMake(btnHelpView.frame.size.width-100,100, 100, 100);
    
    [btnHelpView setImage:[UIImage imageNamed:@"BiteFX_Help.png"] forState:UIControlStateNormal];
    
    videoUrlArr = [[NSMutableArray alloc]init];
    
    m_indicaterView = [[UIView alloc]init];
    m_indicaterView.frame = CGRectMake(0, 0, 929, 748);
    m_indicaterView.backgroundColor = [UIColor clearColor];
    
    lbl_view = [[UILabel alloc]init];
    lbl_view.frame = CGRectMake(327, 374, 500, 50);
    [lbl_view setTextColor:defineBlackColor];
    lbl_view.backgroundColor = [UIColor clearColor];
    lbl_view.font = [UIFont boldSystemFontOfSize:60];
    lbl_view.textAlignment = NSTextAlignmentLeft;
    [lbl_view setHighlightedTextColor:defineBlackColor];
    lbl_view.alpha=1.0;
    
    [m_playerView addSubview:lbl_view];
    
    
    lbl_view_title = [[UILabel alloc]init];
    lbl_view_title.frame = CGRectMake(0, 0, m_playerView.frame.size.width, 53);
    [lbl_view_title setTextColor:defineWhiteColor];
    lbl_view_title.backgroundColor = [UIColor colorWithRed:0.106 green:0.129 blue:0.157 alpha:0.45];
    lbl_view_title.textAlignment = NSTextAlignmentCenter;
    [lbl_view_title setHighlightedTextColor:defineBlackColor];
    lbl_view_title.alpha=0.0;
    lbl_view_title.font = [UIFont fontWithName:@"Arial" size:25];
    lbl_view_title.font = [UIFont boldSystemFontOfSize:25];
    lbl_view_title.adjustsFontSizeToFitWidth=YES;
    lbl_view_title.minimumScaleFactor=0.5;
    
    [self.view addSubview:lbl_view_title];
    [self.view bringSubviewToFront:lbl_view_title];
    m_indicaterView.hidden = YES;
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(playerItemDidReachEnd:)
                                                 name:AVPlayerItemDidPlayToEndTimeNotification
                                               object:nil];
    
    
    //  UIImage*  stetchLeftTrack   = [[UIImage imageNamed:@"SliderFilled.png"]    stretchableImageWithLeftCapWidth:0.0 topCapHeight:0.0];
    
    //  UIImage* stetchRightTrack = [[UIImage imageNamed:@"SliderArea.png"]    stretchableImageWithLeftCapWidth:10.0 topCapHeight:0.0];
    
    [self setSliderProperty];
    
    (self.m_playerView).backgroundColor = [UIColor clearColor];
    [imgview_fullImage setUserInteractionEnabled:YES];
    recognizer1=[[UISwipeGestureRecognizer alloc]initWithTarget:self action:@selector(leftswipe:)];
    recognizer1.direction=UISwipeGestureRecognizerDirectionLeft;
    [m_playerView addGestureRecognizer:recognizer1];
    
    recognizer2=[[UISwipeGestureRecognizer alloc]initWithTarget:self action:@selector(Rightswipe:)];
    recognizer2.direction=UISwipeGestureRecognizerDirectionRight;
    [m_playerView addGestureRecognizer:recognizer2];
    
    recognizer3=[[UISwipeGestureRecognizer alloc]initWithTarget:self action:@selector(leftswipe:)];
    recognizer3.direction=UISwipeGestureRecognizerDirectionLeft;
    
    [imgview_fullImage addGestureRecognizer:recognizer3];
    
    recognizer4=[[UISwipeGestureRecognizer alloc]initWithTarget:self action:@selector(Rightswipe:)];
    recognizer4.direction=UISwipeGestureRecognizerDirectionRight;
    [imgview_fullImage addGestureRecognizer:recognizer4];
    
    mutArrUpdateDownload = [[NSMutableArray alloc] init];
    
    [lblFramcounter setAdjustsFontSizeToFitWidth:YES];
    lblFramcounter.minimumScaleFactor = 0.5;
    
    dateFormatter = [[NSDateFormatter alloc] init];
    dateFormatter.dateFormat = @"yyyy-MM-dd HH:mm:ss";
    [self AddButtonShowHideImgs];
    
    isNewVersionDataDownloading = NO;
    isPreserveUserDefineSequences = NO;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    AppDelegateobj.isDownloadCancelled = FALSE;
    
    // Version 2.4 change...
    // Initially hide btnRemainDownload.
    switch ([UserDefaults  integerForKey:@"UserStatusSubscription"]) {
        case 1:
            userSubStat = InAppSubscribed;
            btnRemainDownload.hidden = YES;
            break;
            
        case 2:
            userSubStat = Subscribed;
            btnRemainDownload.hidden = YES;
            break;
            
        default:
            userSubStat = NotSubscribed;
            btnRemainDownload.hidden = YES;
            break;
    }
    
    progressBar.hidden = YES;
    
    if(btnAnimationPanel.selected) {
        
        btnAnimationPanel.selected=!btnAnimationPanel.selected;
    }
    AppDelegateobj.isFirstScrollView = FALSE;
    
#pragma mark --------------------------------
#pragma mark Version 2.4 Changes
    
    // Version 2.4 change...
    // Check Full-Version is downloaded or not...
    int aIntRegistered  = [UserDefaults boolForKey:@"Registered"];
    
    if (aIntRegistered) {
        
        NSMutableArray *arrAllFile = [[NSMutableArray alloc]init];
        NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
        [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
        
        if (arrAllFile.count > 0) {
            
            [UserDefaults setBool:NO forKey:@"FullDownloaded"];
        }
        else
        {
            [UserDefaults setBool:YES forKey:@"FullDownloaded"];
        }
        [UserDefaults synchronize];
    }
    
    // Version 2.4 change...
    // XML Parsing only for basic version...
    int Fulldownload    = [UserDefaults boolForKey:@"FullDownloaded"];
    
    //
    if (!Fulldownload) {
        [self startXMLParsing];
    }
    
    // Version 2.4 change...
    // Check new data for images and presentations for logged in user is downloaded or not...
    //    if (aIntRegistered) {
    //        [self checkVersionAndDownloadNewData];
    //    }
    
    // Version 2.5 change...
    // Delete Updates when app version is changed.
    //    if (aIntRegistered) {
    [self deleteOldUpdatesDataForNewAppVersion];
    //    }
    
    [self insertPlayListCollection];
    
    if ([[UserDefaults  objectForKey:@"XML_Parse"] isEqualToString:@"0"] )
    {
        [UserDefaults  setObject:@"1" forKey:@"XML_Parse"];
        [UserDefaults  synchronize];
        [self startXMLParsing];
        [self insertPlayListCollection];
    }
    
    lblLicenceEmail.text=@"";
    
    if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
        [self getDataAboutUserFromDatabase];
    }
    if(verticalToolbarView) {
        [verticalToolbarView removeFromSuperview];
    }
    
    UIImage *aimageSpeedSlidernormal = [UIImage imageNamed:@"SpeedThumbNormal.png"];
    UIImage *aimageSpeedSliderHighlighted = [UIImage imageNamed:@"SpeedThumbSelected.png"];
    UIImage *stetchLeftTrack = [[UIImage imageNamed:@"speedslidernewimg.png"]
                                stretchableImageWithLeftCapWidth:10.0 topCapHeight:0.0];
    UIImage *stetchRightTrack = [[UIImage imageNamed:@"speedslidernewimg.png"]
                                 stretchableImageWithLeftCapWidth:10.0 topCapHeight:0.0];
    
    [self.sliderSpeedControl setMinimumTrackImage:stetchLeftTrack forState:UIControlStateNormal];
    [self.sliderSpeedControl setMaximumTrackImage:stetchRightTrack forState:UIControlStateNormal];
    [self.sliderSpeedControl setThumbImage:aimageSpeedSlidernormal forState:UIControlStateNormal];
    [self.sliderSpeedControl setThumbImage:aimageSpeedSliderHighlighted forState:UIControlStateHighlighted];
    self.sliderSpeedControl.minimumValue=0.1;
    self.sliderSpeedControl.maximumValue=2.0;
    self.sliderSpeedControl.continuous=NO;
    
    m_playerView.frame = CGRectMake(0, 0, 929, 696);
    imgview_fullImage.frame = CGRectMake(0, 0, 929, 696);
    verticalToolbarView.frame = CGRectMake(929, 0, 95, 696);
    
    [self.view addSubview:verticalToolbarView];
    
    
    //This will check for any internal updates available
    
    if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
        [self checkForIntervalUpdate];
    }
    
    //    //This will make first video play while application starts.
    if ([[UserDefaults  objectForKey:@"FirstTimeVideo"] isEqualToString:@"1"]) {
        
        [UserDefaults  setObject:@"2" forKey:@"FirstTimeVideo"];
        [UserDefaults  synchronize];
        
        [self getFirstCollectionVideoDetail];
        
        //This will play the very first video of the animation-panel of first row.
        [self playVideoAtIndex:0];
        [self.m_queueplayer pause];
        [self setPlayPauseImageForNormalState];
    }
    
    if (![UserDefaults  boolForKey:@"Launch Bitefx"]) {
        [UserDefaults  setBool:YES forKey:@"Launch Bitefx"];
        [UserDefaults  synchronize];
        
        //        if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
        //            [self callWSForCurrentDate];
        //        }
        
#pragma mark --------------------------------
#pragma mark Version 2.5 checkLoginSubscriptionStatus is added.
        
        /* checkLoginSubscriptionStatus is added for Login user to know user's subscription status and remaining days. This new function is added because we were not getting remaining days value for logged in user in checkSubscriptionStatus method.
         checkSubscriptionStatus is used for InApp user to know user's subscription status and remaining days. */
        if (userSubStat == Subscribed) {
//            [self callWSForCheckLoginSubscriptionStatus];
        }
        else if (userSubStat == InAppSubscribed) {
//            [self callWSForCheckSubscriptionStatus];
        }
        
    }
    //    [self checkForRemainDownload];
    
    // Testing purpose...
    //    [self updateIsCompletedForVersion:28];
    
    //    [self newUpdatedXMLParser];
    //    [self newFeatureXMLParser];
    
    //===================
    // Version 3.0 Changes
    //===================
    // UIWebView is replaced with WKWebView.
    [self setupWebViewPurchaseDetails];
    
    //===================
    // Version 3.0 Changes
    //===================
    // BiteFX start with the Presentation Template panel displayed along with the Info topic for the Example Presentation Template for Basic version...
    if (userSubStat == NotSubscribed) {
        [self startWithPresentatioForBasicVersion];
    }
    
}

- (void)startWithPresentatioForBasicVersion {
    
    // Select Presentation tab...
    appDelegate.intSelectedTab = 2;
    appDelegate.isReOpenPresentationInfo = YES; // To show Example Presentation Template...
    [self btnAnimationclick:btnAnimationPanel];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}
- (void)manageUiEvents {
    
    [self.m_queueplayer pause];
    btnInfo.enabled = NO;
    //    btnHelp.enabled=NO;
    //    btnMenu.enabled=NO;
    btnAnimationPanel.enabled=NO;
    btnNextFrame.enabled=NO;
    btnPreviousFrame.enabled=NO;
    self.sliderSpeedControl.enabled=NO;
    sliderTimeForVideo.enabled=NO;
    playPauseBtn.enabled=NO;
    loopBtn.enabled=NO;
    btn_SpeedControl.alpha = 0.5f;
}

-(void)AddButtonShowHideImgs
{
    btnHideUnhideImages = [UIButton buttonWithType:UIButtonTypeCustom];
    //    btnHideUnhideImages.frame = CGRectMake(725,702,75,44);
    // Version 3.0 Changes
    // New frame change for Small, Medium and Large buttons...
    btnHideUnhideImages.frame = CGRectMake(725,702,75,44);
    [btnHideUnhideImages setImage:[UIImage imageNamed:btnUnHideImage] forState:UIControlStateNormal];
    [btnHideUnhideImages setImage:[UIImage imageNamed:btnHideImage] forState:UIControlStateSelected];
    [btnHideUnhideImages addTarget:self action:@selector(btnHideUnhideImagesClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:btnHideUnhideImages];
    btnHideUnhideImages.hidden = YES;
    
}

- (void)setupWebViewPurchaseDetails {
    
    WKWebViewConfiguration *configuration = [[WKWebViewConfiguration alloc] init];
    webViewPurchaseDetails = [[WKWebView alloc] initWithFrame:CGRectMake(0, 0, viewPurchaseDetails.frame.size.width, viewPurchaseDetails.frame.size.height) configuration:configuration];
    webViewPurchaseDetails.navigationDelegate = self;
    
    viewPurchaseDetails.layer.cornerRadius = 10;
    viewPurchaseDetails.layer.masksToBounds = YES;
    webViewPurchaseDetails.layer.cornerRadius = 10;
    webViewPurchaseDetails.layer.masksToBounds = YES;
    
    tblViewPurchaseOption.layer.cornerRadius = 10;
    tblViewPurchaseOption.layer.masksToBounds = YES;
    
    [viewPurchaseDetails addSubview:webViewPurchaseDetails];
}

// Version 3.0 change
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    
    // WkWebView does not open iTunes url...
    // Handle manage subscription url in WkWebView...
    NSURL *webURL = navigationAction.request.URL;
    if ([webURL.host isEqualToString:@"apps.apple.com"])
    {
        UIApplication *app = [UIApplication sharedApplication];
        if ([app canOpenURL:webURL]) {
            
            [webViewPurchaseDetails stopLoading];
            [app openURL:[NSURL URLWithString:webURL.absoluteString] options:@{} completionHandler:^(BOOL success) {
                if (success) {
                    NSLog(@"Opened WebView url");
                }
            }];
            decisionHandler(WKNavigationActionPolicyCancel);
        } else {
            decisionHandler(WKNavigationActionPolicyCancel);
        }
    }
    else {
        decisionHandler(WKNavigationActionPolicyAllow);
    }
    return;
}

-(void)btnHideUnhideImagesClicked:(UIButton*)sender
{
    if (sender.isSelected)
    {
        [appDelegate hideShowAllImportedImages:@"1"];
        for (int i=0; i < AppDelegateobj.mutArrPlayVideo.count; i++)
        {
            NSMutableDictionary *objDict = AppDelegateobj.mutArrPlayVideo[i];
            objDict[@"IsHidden"] = @"1";
        }
        sender.selected = NO;
    }
    else
    {
        [appDelegate hideShowAllImportedImages:@"0"];
        for (int i=0; i < appDelegate.mutArrPlayVideo.count; i++)
        {
            NSMutableDictionary *objDict = appDelegate.mutArrPlayVideo[i];
            objDict[@"IsHidden"] = @"0";
        }
        sender.selected = YES;
    }
    [self mangePhotoVideoFlow:AppDelegateobj.intSelectedVideo];
}

#pragma mark -
#pragma mark Webservice request and response methods
- (void)callRegisterwebService {
    
    btnRegister.enabled = NO;
    [viewRegistration endEditing:YES];
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    self.strSerialNumber = [NSString stringWithFormat:@"%@",txtFieldSerialnumber1.text];
    
    body[@"method"] = LOGIN;
    body[@"emailID"] = self.txtFieldEmail.text;
    
    self.strEmail = [NSString stringWithFormat:@"%@",self.txtFieldEmail.text];
    body[@"password"] = self.strSerialNumber;
    body[@"udid"] = UDIDForWS;
    //    CallWebService *webservice;
    
    //    webservice =[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"RegisterUser"];
    //    return;
    WebService *aWebService = [[WebService alloc] init];
    [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
        
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            self->btnRegister.enabled = YES;
            return;
        }
        NSDictionary *aDictResponse = object;
        [self registerUser:aDictResponse];
    }];
    
    
}
/* Not in use...
 - (void)callDownloadWebservice {
 
 NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
 [body setObject:@"updatesList3"forKey:@"method"];
 [body setObject:@"6" forKey:@"update_version"];
 
 NSString *aStrNameQuery = [NSString stringWithFormat:selectAllDataFromUserTable];
 NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
 
 [body setObject:[[aMutArrNames objectAtIndex:0] objectForKey:@"emailId"] forKey:@"emailID"];
 [body setObject:[[aMutArrNames objectAtIndex:0] objectForKey:@"SerialNumber"] forKey:@"serialNumber"];
 CallWebService *webservice;
 webservice=[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"User"];
 }
 */

- (void)callWSforInAppVarification {
    
    //===================
    // Version 3.0 Changes
    //===================
    // Show activity indicator...
    [appDelegate startIndicator];
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    body[@"method"] = VERIFY_PURCHASE;
    NSMutableArray *aMutArr = [[NSMutableArray alloc] init];
    NSString *strReceipt = @"";
    if ([skPaymentTrasCurrent isKindOfClass:[SKPaymentTransaction class]])
    {
        if  (skPaymentTrasCurrent)
        {
            strReceipt = [Utility getInAppPurchaseReceipt];
        }
    }
    else
    {
        NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
        NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
        
        if (aMutArrNames.count > 0)
        {
            strReceipt = aMutArrNames[0][@"Receipt"];
        }
        else
        {
            if ([UserDefaults  objectForKey:@"Receipt"])
            {
                strReceipt = [UserDefaults  objectForKey:@"Receipt"];
            }
            if ([UserDefaults  objectForKey:@"InsertQuery"])
            {
                [SharedDatabase Insert:[UserDefaults  objectForKey:@"InsertQuery"]];
            }
        }
    }
    
    strReceipt = [Utility getInAppPurchaseReceipt];
    [aMutArr addObject:strReceipt];
    body[@"receipts"] = aMutArr;
    body[@"udid"] = UDIDForWS;
    
    //    CallWebService *webservice;
    //    webservice =[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"InAppVarification"];
    
    WebService *aWebService = [[WebService alloc] init];
    [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
        
        //===================
        // Version 3.0 Changes
        //===================
        // Hide activity indicator...
        [appDelegate stopIndicator];
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            return;
        }
        NSDictionary *aDictResponse = object;
        
        
        
        // BEGIN DEBUG DEBUG DEBUG
        NSString *stsCode = aDictResponse[@"status_code"];
        if([stsCode isKindOfClass:[NSNull class]] || stsCode == nil || [stsCode.lowercaseString containsString:@"nil"] || [stsCode.lowercaseString containsString:@"null"]) {
            NSString *bodyStr = [[NSString alloc] initWithData:[NSJSONSerialization dataWithJSONObject:body options:NSJSONWritingPrettyPrinted error:nil] encoding:NSUTF8StringEncoding];
            [UIAlertController showAlertInViewController:self withTitle:@"TestFlight Data" message:bodyStr cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        // END DEBUG DEBUG DEBUG
        
        
        
        
        [self InAppVarification:aDictResponse];
    }];
    
    
}

- (void)mailComposeController:(MFMailComposeViewController *)controller didFinishWithResult:(MFMailComposeResult)result error:(nullable NSError *)error {
    if (!error) {
        NSLog(@"Success");
    }
}
- (void)callWSforInAppVarification:(NSString *)aStrReceipt {
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    body[@"method"] = @"verifyPurchase3";
    NSMutableArray *aMutArr = [[NSMutableArray alloc] init];
    [aMutArr addObject:aStrReceipt];
    body[@"receipts"] = aMutArr;
    body[@"udid"] = UDIDForWS;
    CallWebService *webservice;
    
    webservice =[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"InAppVarification"];
}

#pragma mark --------------------------------
#pragma mark Version 2.5 checkLoginSubscriptionStatus is added.

- (void)callWSForCheckLoginSubscriptionStatus {
    
    // Get loggedin user data...
    NSString *aStrNameQuery = [NSString stringWithFormat:selectAllDataFromUserTable];
    NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
    
    self.strEmail = aMutArrNames[0][@"emailId"];
    self.strSerialNumber = aMutArrNames[0][@"SerialNumber"];
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    body[@"method"] = CHECK_LOGIN_SUBSCRIPTION_STATUS;
    body[@"emailID"] = self.strEmail;
    body[@"serialNumber"] = self.strSerialNumber;
    
    WebService *aWebService = [[WebService alloc] init];
    [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
        
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            return;
        }
        NSDictionary *aDictResponse = object;
        [self checkLoginSubscription:aDictResponse];
    }];
    
}

#pragma mark --------------------------------
#pragma mark Version 2.5 callWSForCurrentDate is renamed with callWSForCheckSubscriptionStatus

- (void)callWSForCheckSubscriptionStatus {
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    body[@"method"] = CHECK_SUBSCRIPTION_STATUS;
    
    NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
    NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
    NSString *aStrReceipt = @"";
    if (aMutArrNames.count > 0)
    {
        aStrReceipt = aMutArrNames[0][@"Receipt"];
    }
    else
    {
        // Version 2.5 change...
        // First time Receipt value nil from database...
        if ([UserDefaults  objectForKey:@"Receipt"])
        {
            aStrReceipt = [UserDefaults  objectForKey:@"Receipt"];
        }
    }
    
    aStrReceipt = [Utility getInAppPurchaseReceipt];
    body[@"receipt"] = aStrReceipt;
    body[@"udid"] = UDIDForWS;
    
    WebService *aWebService = [[WebService alloc] init];
    [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
        
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            return;
        }
        NSDictionary *aDictResponse = object;
        [self checkSubscription:aDictResponse];
    }];
    
}

- (void)callWSForCurrentDate {
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    body[@"method"] = CHECK_SUBSCRIPTION_STATUS;
    if (userSubStat == InAppSubscribed)
    {
        NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
        NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
        NSString *aStrReceipt = @"";
        if (aMutArrNames.count > 0)
        {
            aStrReceipt = aMutArrNames[0][@"Receipt"];
        }
        body[@"receipt"] = aStrReceipt;
        
    }
    else if (userSubStat == Subscribed)
    {
        body[@"receipt"] = @"234";
    }
    body[@"udid"] = UDIDForWS;
    //    CallWebService *webservice;
    
    //    webservice =[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"CurrentDate"];
    //    return;
    
    WebService *aWebService = [[WebService alloc] init];
    [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
        
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            return;
        }
        NSDictionary *aDictResponse = object;
        [self checkSubscription:aDictResponse];
    }];
    
    
}

- (void)responsedidreceive:(NSMutableData *)response_data forKey:(NSString *)reskey {
    NSString *returnStr = [[NSString alloc]initWithData:response_data encoding:NSUTF8StringEncoding];
    NSDictionary *responsedictionary= (NSDictionary*)[returnStr JSONValue];
    if ([reskey isEqualToString:@"RegisterUser"]) {
        [self registerUser:responsedictionary];
    }
    else if ([reskey isEqualToString:@"User"]) {
    }
    else if ([reskey isEqualToString:@"DeactivateUser"])
    {
        [self deactiveUser:responsedictionary];
    }
    else if ([reskey isEqualToString:@"Check Update"]) {
        [self checkUpdate:responsedictionary];
    }
    else if ([reskey isEqualToString:@"Check Update InApp"])
    {
        [self checkUpdateInApp:responsedictionary];
    }
    else if ([reskey isEqualToString:@"InAppVarification"]) {
        [self InAppVarification:responsedictionary];
    }
    else if ([reskey isEqualToString:@"CurrentDate"]) {
        [self currentDate:responsedictionary];
    }
}
/* older function
 -(void)currentDate:(NSDictionary *)responsedictionary
 {
 
 int intStatus = [[responsedictionary objectForKey:@"status"] intValue];
 if(intStatus == 1)
 {
 if ([[responsedictionary objectForKey:@"status_code"] isEqualToString:@"112"]) {
 NSString *aStrDa = [responsedictionary objectForKey:@"subscription_expiry"];
 if(aStrDa){
 strCurrentDate = [self strGetDate:aStrDa];
 [self checkForAvaibility];
 }else if (responsedictionary[@"remainingDays"]){
 int intRemaingingDays = (int)[responsedictionary[@"remainingDays"]integerValue];
 if(intRemaingingDays==0)
 {
 [self moveToBasicVersion];
 }
 else
 {
 // Version 2.4 change
 // btnRemainDownload is initially hidden so set it's status...
 [self checkForRemainDownload];
 }
 }else{
 [self checkForRemainDownload];
 }
 }else{
 [self checkForRemainDownload];
 }
 }else{
 [self checkForRemainDownload];
 }
 }
 */

-(void)checkLoginSubscription:(NSDictionary *)responsedictionary
{
    int intStatus = [responsedictionary[@"status"] intValue];
    int intStatusCode = [responsedictionary[@"status_code"] intValue];
    
    // For success StatusCode is 111...
    if (intStatus == 1 && intStatusCode == 111) {
        
        if (responsedictionary[@"remainingDays"]) {
            int intRemaingingDays = (int)[responsedictionary[@"remainingDays"] integerValue];
            if(intRemaingingDays<=0)
            {
                // Show subscription expired message...
                [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:LOGIN_USER_SUBSCRPTION_EXPIRED cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                    
                    // App will be moved to basic version...
                    [self moveToBasicVersion];
                }];
                
            }
            else if(intRemaingingDays < 20 && intRemaingingDays > 0) {
                
                // Show user subscription will expire in n days...
                NSString *aStrMessage = [NSString stringWithFormat:LOGIN_USER_SUBSCRPTION_REMAINING_DAYS, intRemaingingDays];
                [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrMessage cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            }
            else
            {
                // Remaining days are more than 20, so app opens with full content...
                // Version 2.4 change
                // btnRemainDownload is initially hidden so set it's status...
                [self checkForRemainDownload];
            }
        }
    }
    else {
        
        // Version 2.5 Changes...
        // Here StatusCodes can be 407...
        NSString *aStrResponse = responsedictionary[@"message"];
        
        if (intStatusCode == 407) {
            
            // Membership inactive or expired...
            
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrResponse cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                
                // Disable FullContent...
                [self disableFullContentAndShowBasicVersion];
            }];
            
        }
        else {
            
            // Display the error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrResponse cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        
    }
}

-(void)checkSubscription:(NSDictionary *)responsedictionary
{
    NSString *aStrResponse = responsedictionary[@"message"];
    
    NSString *stsCode = responsedictionary[@"status_code"];
    
    if([stsCode isKindOfClass:[NSNull class]] || stsCode == nil || [stsCode.lowercaseString containsString:@"nil"] || [stsCode.lowercaseString containsString:@"null"]) {
        // SCM - 2023-07-06 Unique error message differentiates this from other identical errors in code. It seems like this validation should have been done BEFORE this
        //                  call was made (i.e., closer to the original HTTP/HTTPS request), but I don't want to change critical and/or brittle code casually.
        NSString *errMsg = [ErrorFormatter formatErrorMsgWithBaseString:SOME_THING_WENT_WRONG_ERROR errorType:CHECK_SUBSCRIPTION_BAD_RESPONSE_ERROR serverReturnCode:BFX_UNKNOWN];
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:errMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        return;
    }
    int intStatus = [responsedictionary[@"status"] intValue];
    int intStatusCode = stsCode.intValue;
    
    // For success StatusCode is 104...
    if (intStatus == 1 && intStatusCode == 104) {
        
        if (responsedictionary[@"remainingDays"]) {
            int intRemaingingDays = (int)[responsedictionary[@"remainingDays"] integerValue];
            if(intRemaingingDays<=0)
            {
                // Show subscription expired message...
                UIAlertController *alertController = [UIAlertController alertControllerWithTitle:strAlertTitle message:INAPP_USER_SUBSCRPTION_EXPIRED preferredStyle:UIAlertControllerStyleAlert];
                
                UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    // App will be moved to basic version...
                    [self moveToBasicVersion];
                }];
                [alertController addAction:okAction];
                
                UIAlertAction *renewAction = [UIAlertAction actionWithTitle:strAlertRenewSubscriptionTitle style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    // App will be moved to basic version...
                    [self moveToBasicVersion];
                    
                    // Show InApp purchase dialog to renew subscription...
                    [self PurchaseAction:nil];
                }];
                [alertController addAction:renewAction];
                
                [self presentViewController:alertController animated:YES completion:nil];
            }
            else if(intRemaingingDays < 20 && intRemaingingDays > 0) {
                
                // Show user subscription will expire in n days...
                NSString *aStrMessage = [NSString stringWithFormat:INAPP_USER_SUBSCRPTION_REMAINING_DAYS, intRemaingingDays];
                UIAlertController *alertController = [UIAlertController alertControllerWithTitle:strAlertTitle message:aStrMessage preferredStyle:UIAlertControllerStyleAlert];
                
                UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil];
                [alertController addAction:okAction];
                
                UIAlertAction *renewAction = [UIAlertAction actionWithTitle:strAlertRenewSubscriptionTitle style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    // Show InApp purchase dialog to renew subscription...
                    //                    [self PurchaseAction:nil];
                }];
                [alertController addAction:renewAction];
                
                [self presentViewController:alertController animated:YES completion:nil];
            }
            else
            {
                // Remaining days are more than 20, so app opens with full content...
                // Version 2.4 change
                // btnRemainDownload is initially hidden so set it's status...
                [self checkForRemainDownload];
            }
        }
    }
    else {
        
        NSString *msg = strAlertBiteFXSupport;
        if (aStrResponse.length > 0)
        {
            msg = aStrResponse;
        }
        // SCM - 2023-06-07 Error message now captures current action at time of error and status code returned by server.
        msg = [ErrorFormatter formatErrorMsgWithBaseString:msg errorType:CHECK_SUBSCRIPTION_ERROR serverReturnCode:intStatusCode];
        
        // Status code 106 is also for success but it shows subscription is in grace period...
        if (intStatus == 1 && intStatusCode == 106) {
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:SUBSCRIPTION_GRACE_PERIOD_MESSAGE cancelButtonTitle:strAlertContinueTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        } else if (intStatusCode == 400) {
            [self checkForError400:msg];
        } else if (intStatusCode >= 431 && intStatusCode <= 435) {
            [self checkForError431to435:intStatusCode];
        }
        // Version 2.5 Changes...
        else if (intStatusCode == 407) {
            // Membership inactive or expired...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                
                // Disable FullContent...
                [self disableFullContentAndShowBasicVersion];
            }];
        }
        else {
            // Display the error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
    }
}

-(void)currentDate:(NSDictionary *)responsedictionary
{
    int intStatus = [responsedictionary[@"status"] intValue];
    NSString *strStatusCode = responsedictionary[@"status_code"];
    if(intStatus == 1)
    {
        if ([strStatusCode isEqualToString:@"112"])
        {
            NSString *aStrDa = responsedictionary[@"subscription_expiry"];
            if(aStrDa){
                strCurrentDate = [self strGetDate:aStrDa];
                [self checkForAvaibility];
            }else if (responsedictionary[@"remainingDays"]){
                int intRemaingingDays = (int)[responsedictionary[@"remainingDays"]integerValue];
                if(intRemaingingDays==0)
                {
                    [self moveToBasicVersion];
                }
                else
                {
                    // Version 2.4 change
                    // btnRemainDownload is initially hidden so set it's status...
                    [self checkForRemainDownload];
                }
            }else{
                [self checkForRemainDownload];
            }
        }else{
            [self checkForRemainDownload];
        }
    }
    //    else if(intStatus == 0 && ([strStatusCode isEqualToString:@"400"] || [strStatusCode isEqualToString:@"401"] || [strStatusCode isEqualToString:@"402"] || [strStatusCode isEqualToString:@"403"] || [strStatusCode isEqualToString:@"409"] || [strStatusCode isEqualToString:@"410"] || [strStatusCode isEqualToString:@"411"] || [strStatusCode isEqualToString:@"412"] || [strStatusCode isEqualToString:@"418"] || [strStatusCode isEqualToString:@"419"] || [strStatusCode isEqualToString:@"420"] || [strStatusCode isEqualToString:@"421"] || [strStatusCode isEqualToString:@"422"] || [strStatusCode isEqualToString:@"423"] || [strStatusCode isEqualToString:@"424"] || [strStatusCode isEqualToString:@"426"]))
    //    {
    //        [self ShowAlertMessageWithOkAndCancelTitle:strAlertTitle alertMessage:[responsedictionary objectForKey:@"message"] cancelButtonTitle:@"OK" alertTag:2001];
    //        [self moveToBasicVersion];
    //    }
    
    else{
        [self checkForRemainDownload];
    }
}

// New Version 2.5 change
- (void)disableFullContentAndShowBasicVersion {
    
    [self moveToBasicVersion];
}

-(void)checkForError400: (NSString*)msg {
    if ([msg.lowercaseString isEqualToString:@"Invalid Receipt Error".lowercaseString]) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"Invalid Receipt Error – Please contact BiteFX." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
    else if ([msg.lowercaseString isEqualToString:@"BiteFX Server Error".lowercaseString]) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"BiteFX Server Error – Please contact BiteFX." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
    else if ([msg.lowercaseString isEqualToString:@"App Store Server not currently available".lowercaseString]) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"App Store Server not currently available – Please try again later." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
    else if ([msg.lowercaseString isEqualToString:@"Internal data access error".lowercaseString]) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"Internal data access error – Please contact BiteFX." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
    else {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
}

-(void)checkForError431to435:(int)intStatusCode {
    if (intStatusCode == 431) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"It appears you have cancelled your BiteFX subscription. BiteFX will revert to its basic (free) content." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
            
            // Disable FullContent...
            [self disableFullContentAndShowBasicVersion];
        }];
    }
    
    else if (intStatusCode == 432) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"The App Store reports that there was a billing error when charging your BiteFX subscription. As the grace period has passed BiteFX will revert to its basic (free) content." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
            
            // Disable FullContent...
            [self disableFullContentAndShowBasicVersion];
        }];
    }
    
    else if (intStatusCode == 433) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"The App Store reports that you did not agree to a recent price increase. BiteFX will therefore revert to its basic (free) content." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
            
            // Disable FullContent...
            [self disableFullContentAndShowBasicVersion];
        }];
    }
    
    else if (intStatusCode == 434) {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"It appears that BiteFX is no longer available for purchase. Please check the BiteFX website, bitefx.com, for further information. BiteFX will revert to its basic (free) content." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
            
            // Disable FullContent...
            [self disableFullContentAndShowBasicVersion];
        }];
    }
    
    else {
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"The App Store reports there is an unknown error on your BiteFX subscription. Please contact the App Store to resolve the issue. BiteFX will revert to its basic (free) content." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
            
            // Disable FullContent...
            [self disableFullContentAndShowBasicVersion];
        }];
    }
}

-(void)startDownloadAfterInappSuccess:(NSDictionary *)responsedictionary {
    isRegistered=TRUE;
    FullorUpdate = 1;
    isSubscribed = TRUE;
    
    //===================
    // Version 3.0 Changes
    //===================
    // Save status InAppSubscribed after we get success response in API...
    userSubStat = InAppSubscribed;
    [UserDefaults  setInteger:InAppSubscribed forKey:@"UserStatusSubscription"];
    [UserDefaults synchronize];
    
    //===================
    // Version 3.0 Changes
    //===================
    // Below code is not necessary...
    //    if ([UserDefaults  boolForKey:@"InAppContinue"]) {
    //        [UserDefaults  setBool:NO forKey:@"InAppContinue"];
    //        [UserDefaults  synchronize];
    //    } else {
    //        [self callCompleteTransactoionAfterCompletion];
    //    }
    
    NSString *astrEmail = responsedictionary[@"user_info"][@"email"];
    [UserDefaults setObject:astrEmail forKey:@"email"];
    [viewRegistration removeFromSuperview];
    
    if (viewPurchase.superview) {
        [viewPurchase removeFromSuperview];
    }
    
    alertForUpdateMessage = [UIAlertController alertControllerWithTitle:ErrorTitle message:UPDATE_SUCCESS_MESSAGE preferredStyle:UIAlertControllerStyleAlert];
    [self presentViewController:alertForUpdateMessage animated:YES completion:nil];
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
        // Download Full Version and save data...
        [self saveInAppVerificationInfoAndDownloadFullContent:responsedictionary];
        
    });
}

-(void)InAppVarification:(NSDictionary *)responsedictionary
{
    NSString *aStrResponse = responsedictionary[@"message"];
    
    NSString *stsCode = responsedictionary[@"status_code"];
    
    if([stsCode isKindOfClass:[NSNull class]] || stsCode == nil || [stsCode.lowercaseString containsString:@"nil"] || [stsCode.lowercaseString containsString:@"null"]) {
        // SCM - 2023-07-06 Unique error message differentiates this from other identical errors in code. It seems like this validation should have been done BEFORE this
        //                  call was made (i.e., closer to the original HTTP/HTTPS request), but I don't want to change critical and/or brittle code casually.
        NSString *errMsg = [ErrorFormatter formatErrorMsgWithBaseString:SOME_THING_WENT_WRONG_ERROR errorType:IN_APP_VERIFICATION_BAD_RESPONSE_ERROR serverReturnCode:BFX_UNKNOWN];
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:errMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        
        
        
        
        // BEGIN --- DEBUG DEBUG DEBUG DEBUG
        NSMutableString *resultString = [NSMutableString string];
        
        for (NSString *key in responsedictionary) {
            id value = responsedictionary[key];
            
            NSString *elementString = @"";
            if ([value isKindOfClass:[NSString class]]) {
                // Limit the element string to 40 characters
                NSString *limitedValue = [value substringToIndex:MIN([value length], 40)];
                elementString = limitedValue;
            } else {
                elementString = [value description];
            }
            
            [resultString appendFormat:@"%@: %@\n", key, elementString];
        }
        
        [UIAlertController showAlertInViewController:self withTitle:@"TestFlight Data" message:resultString cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        // END --- DEBUG DEBUG DEBUG DEBUG
        
        
        
        
        
        return;
    }
    
    int intStatus = [responsedictionary[@"status"] intValue];
    int intStatusCode = stsCode.intValue;
    
    // For success StatusCode is 104...
    if (intStatus == 1 && intStatusCode == 104) {
        
        [self startDownloadAfterInappSuccess:responsedictionary];
        
    } else {
        NSString *msg = strAlertBiteFXSupport;
        if (aStrResponse.length > 0)
        {
            msg = aStrResponse;
        }
        // SCM - 2023-06-07 Error message now captures current action at time of error and status code returned by server.
        msg = [ErrorFormatter formatErrorMsgWithBaseString:msg errorType:IN_APP_VERIFICATION_ERROR serverReturnCode:intStatusCode];
        
        // Satus code 106 is also for success but it shows subscription is in grace period...
        if (intStatus == 1 && intStatusCode == 106) {
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:SUBSCRIPTION_GRACE_PERIOD_MESSAGE cancelButtonTitle:strAlertContinueTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                [self startDownloadAfterInappSuccess:responsedictionary];
            }];
        }
        else if (intStatusCode == 400) {
            [self checkForError400:msg];
        }
        else if (intStatusCode >= 431 && intStatusCode <= 435) {
            [self checkForError431to435:intStatusCode];
        }
        // Version 2.5 Changes...
        // Here StatusCodes can be 404, 405...
        else if (intStatusCode == 405) {
            
            // Membership inactive or expired...
            
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                
                // Remove Purchase view...
                [self->viewRegistration removeFromSuperview];
                
                if (self->viewPurchase.superview) {
                    [self->viewPurchase removeFromSuperview];
                }
                
                // Disable FullContent...
                [self disableFullContentAndShowBasicVersion];
            }];
            
        }
        else if (intStatusCode == 404) {
            // Display the error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        else
        {
            // Display the error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            
        }
        
    }
}

/*
 // ============ old working function before new codes for auto renew subscriptions ============
 -(void)InAppVarification:(NSDictionary *)responsedictionary
 {
 NSString *aStrResponse = [responsedictionary objectForKey:@"message"];
 
 NSString *astrEmail = [[responsedictionary objectForKey:@"user_info"] objectForKey:@"email"];
 
 int intStatusCode = [[responsedictionary objectForKey:@"status_code"] intValue];
 
 // For success StatusCode is 111...
 int intStatus = [[responsedictionary objectForKey:@"status"] intValue];
 if(intStatus == 1 && intStatusCode == 111) {
 
 isRegistered=TRUE;
 FullorUpdate = 1;
 isSubscribed = TRUE;
 
 if ([UserDefaults  boolForKey:@"InAppContinue"]) {
 [UserDefaults  setBool:NO forKey:@"InAppContinue"];
 [UserDefaults  synchronize];
 } else {
 [self callCompleteTransactoionAfterCompletion];
 }
 
 [UserDefaults setObject:astrEmail forKey:@"email"];
 [viewRegistration removeFromSuperview];
 
 if ([viewPurchase superview]) {
 [viewPurchase removeFromSuperview];
 }
 
 alertForUpdateMessage = [UIAlertController alertControllerWithTitle:ErrorTitle message:UPDATE_SUCCESS_MESSAGE preferredStyle:UIAlertControllerStyleAlert];
 [self presentViewController:alertForUpdateMessage animated:YES completion:nil];
 
 dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
 
 // Download Full Version and save data...
 [self saveInAppVerificationInfoAndDownloadFullContent:responsedictionary];
 
 });
 
 } else {
 NSString *msg = strAlertBiteFXSupport;
 if (aStrResponse.length > 0)
 {
 msg = aStrResponse;
 }
 
 // Version 2.5 Changes...
 // Here StatusCodes can be 404, 405...
 if (intStatusCode == 405) {
 
 // Membership inactive or expired...
 
 [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
 
 // Remove Purchase view...
 [self->viewRegistration removeFromSuperview];
 
 if ([self->viewPurchase superview]) {
 [self->viewPurchase removeFromSuperview];
 }
 
 // Disable FullContent...
 [self disableFullContentAndShowBasicVersion];
 }];
 
 }
 else if (intStatusCode == 404) {
 // Display the error message...
 [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
 }
 else
 {
 // Display the error message...
 [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
 
 }
 
 }
 }
 */

- (void)saveInAppVerificationInfoAndDownloadFullContent:(NSDictionary *)responsedictionary {
    
    NSString *aStrUpdadte = [NSString stringWithFormat:@"update UserMaster set emailId = \'%@\', isRegistered = \'1\', isSubscribed = \'1\' , SerialNumber = \'%@\' where userId = \'1\'",self.strEmail,self.strSerialNumber];
    [SharedDatabase Update:aStrUpdadte];
    
    //===================
    // Version 2.4 Changes
    //===================
    // Login3 response is changed, so now we get dictionary instead of array...
    // self.mutArrFullVersion used other places but that is not affecting download logic. (No need to change anything)
    //        self.mutArrFullVersion = [[responsedictionary objectForKey:@"full_version"] retain];
    self.mutDictFullVersion = responsedictionary[@"full_version"];
    
    [self insertIntoDownloadFileList];
    self.aStrMviFile = responsedictionary[@"placement_determination_file"];
    [UserDefaults setObject:self.aStrMviFile forKey:MVI_FILE_URL];
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    //-----------------
    [UserDefaults setObject:responsedictionary[@"features_file"] forKey:FEATURES_MVI_FILE_URL];
    [UserDefaults synchronize];
    [self downloadFeaturesMVI];
    //===================
    
    int FullDownload = [UserDefaults boolForKey:@"FullDownloaded"];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if(FullDownload) {
            [self->alertForUpdateMessage dismissViewControllerAnimated:YES completion:nil];
            [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:@"You have already downloaded" cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        } else {
            
            int aIntUpdate = [UserDefaults boolForKey:@"UpdateDownloaded"];
            if(aIntUpdate) {
                self->imgMenuUpdates.hidden = YES;
                self->imgUpdatesAvailable.hidden = YES;
                self->lblMenuUpdates.hidden = YES;
                self->lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",self->intupdate];
            } else {
                if(self->intupdate>0){
                    self->imgMenuUpdates.hidden = NO;
                    self->imgUpdatesAvailable.hidden = NO;
                    self->lblMenuUpdates.hidden = NO;
                    self->lblMenuUpdates.hidden = NO;
                    self->lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",self->intupdate];
                }
            }
            
            [UserDefaults setBool:YES forKey:@"Registered"];
            [UserDefaults setBool:YES forKey:@"FullDownloaded"];
            [UserDefaults synchronize];
            
            // In App ----
            self->userSubStat = InAppSubscribed;
            
            [UserDefaults setInteger:InAppSubscribed forKey:@"UserStatusSubscription"];
            [UserDefaults synchronize];
            
            //===================
            // Version 3.0 Changes
            //===================
            // New InApp code changes. Commented line..
            //            [appDelegate iCloudIntegrationforDB];
            
            //This will start the downloading.
            // New InApp code changes. Commented API call...
            //            [self callUpdateCheckWebservice:nil];
            self->arrFileDownloadList = [AppDelegate listOfRemainFileDownload];
            [self->alertForUpdateMessage dismissViewControllerAnimated:YES completion:nil];
            [self startFullVersionFileDownload]; // Commented for Bug Fix
            
            //TODO: Bug Fix
            self->shouldStartDownload = YES;
            [self startXMLParsing];
        }
        
    });
}

-(void)checkUpdateInApp:(NSDictionary *)responsedictionary
{
    NSString *aStrResponse = responsedictionary[@"message"];
    
    NSString *stsCode = responsedictionary[@"status_code"];
    
    if([stsCode isKindOfClass:[NSNull class]] || stsCode == nil || [stsCode.lowercaseString containsString:@"nil"] || [stsCode.lowercaseString containsString:@"null"]) {
        // SCM - 2023-07-06 Unique error message differentiates this from other identical errors in code. It seems like this validation should have been done BEFORE this
        //                  call was made (i.e., closer to the original HTTP/HTTPS request), but I don't want to change critical and/or brittle code casually.
        NSString *errMsg = [ErrorFormatter formatErrorMsgWithBaseString:SOME_THING_WENT_WRONG_ERROR errorType:CHECK_UPDATE_IN_APP_BAD_RESPONSE_ERROR serverReturnCode:BFX_UNKNOWN];
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:errMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        return;
    }
    
    int intStatus = [responsedictionary[@"status"] intValue];
    int intStatusCode = stsCode.intValue;
    
    // For success StatusCode is 104...
    if (intStatus == 1 && intStatusCode == 104) {
        [self saveInAppUpdates:responsedictionary];
        
    } else {
        
        isUpdateDownloaded = TRUE;
        lblNoupDates.text = @"No more updates available.";
        
        NSString *msg = strAlertBiteFXSupport;
        if (aStrResponse.length > 0)
        {
            msg = aStrResponse;
        }
        // SCM - 2023-06-07 Error message now captures current action at time of error and status code returned by server.
        msg = [ErrorFormatter formatErrorMsgWithBaseString:msg errorType:CHECK_UPDATE_IN_APP_ERROR serverReturnCode:intStatusCode];
        
        // Satus code 106 is also for success but it shows subscription is in grace period...
        if (intStatus == 1 && intStatusCode == 106) {
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:SUBSCRIPTION_GRACE_PERIOD_MESSAGE cancelButtonTitle:strAlertContinueTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                [self saveInAppUpdates:responsedictionary];
            }];
        }
        
        else if (intStatusCode == 400) {
            [self checkForError400:msg];
        }
        
        else if (intStatusCode >= 431 && intStatusCode <= 435) {
            [self checkForError431to435:intStatusCode];
        }
        
        else if (intStatusCode == 405) {
            
            // Membership inactive or expired...
            
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                
                // Disable FullContent...
                [self disableFullContentAndShowBasicVersion];
            }];
            
        }
        else if (intStatusCode == 404) {
            
            // Display the error message...
            // Note : On click of OK Return to Purchase Dialgog but not feasible...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        else {
            
            // Display the error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        
    }
    [self setUpdateCount];
}

-(void)saveInAppUpdates:(NSDictionary *)responsedictionary {
    
    if ([responsedictionary[@"updates_count"]intValue]>0) {
        
        /* --------------------   In App DB ---------------*/
        
        NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
        
        NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
        
        int days;
        
        days = 30;
        //        if ([UserDefaults  integerForKey:@"Plan"]==1) {
        //            days = 180;
        //        } else {
        //            days = 365;
        //        }
        NSDateFormatter *dtFrmt = [[NSDateFormatter alloc] init];
        
        dtFrmt.dateFormat = @"dd/MM/yyyy";
        
        NSString *aStrDa = responsedictionary[@"subscription_expiry"];
        NSString *aStrEndDt = [self strGetDate:aStrDa];
        
        NSString *strReceipts = [UserDefaults  objectForKey:@"Receipt"] ? [UserDefaults  objectForKey:@"Receipt"] : @"" ;
        
        //NSString *aStrEndDt = [NSString stringWithFormat:@"%@/%@/%@", [[aStrDa substringToIndex:6] substringToIndex:2],[[aStrDa substringFromIndex:4] substringToIndex:2],[aStrDa substringToIndex:4]];
        
        if (aMutArrNames.count > 0) {
            
            NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update InAppTbl set  End_Date = \'%@\' where  ID='1'",aStrEndDt];
            [SharedDatabase Update:aStrUpdateSql];
            
        } else {
            
            NSString *aStrInsert = [NSString stringWithFormat:@"insert into  InAppTbl ('Receipt','Purchase_Date', 'End_Date', 'Created_Date', 'Plan', 'Kind') values (\'%@\',\'%@\', \'%@\', \'%@\', \'%d\', \'%@\')",strReceipts,[dtFrmt stringFromDate:[NSDate date]],aStrEndDt, [dtFrmt stringFromDate:[NSDate date]], days, @"InApp"];
            
            [SharedDatabase Insert:aStrInsert];
        }
        
        /* -------------------------------------*/
        
        if(ArrUpdatedownload) {
            [self.ArrUpdatedownload removeAllObjects];
        }
        
        if(arrMviFile) {
            [self.arrMviFile removeAllObjects];
        }
        if(mutArrTotalmviFiles) {
            [mutArrTotalmviFiles removeAllObjects];
        }
        
        NSMutableArray *arr= responsedictionary[@"update_list"];
        
        int counter=1;
        for(int i=0;i<arr.count;i++) {
            
            NSString *strMVIFile = arr[i][@"placement_determination_file"];
            NSString *astr = arr[i][@"version_number"];
            
            NSMutableDictionary *dict =[[NSMutableDictionary alloc]init] ;
            NSMutableDictionary *mutDict = [[NSMutableDictionary alloc]init];
            [dict setValue:strMVIFile forKey:@"mviFile"];
            [mutDict setValue:astr forKey:@"version"];
            [self.arrMviFile addObject:dict];
            [mutArrTotalmviFiles addObject:mutDict];
            counter++;
        }
        
        NSSortDescriptor *sortDescriptor;
        sortDescriptor = [[NSSortDescriptor alloc] initWithKey:@"mviFile" ascending:YES];
        [self.arrMviFile sortUsingDescriptors:@[sortDescriptor]];
        NSSortDescriptor *sortDescriptor1;
        sortDescriptor1 = [[NSSortDescriptor alloc] initWithKey:@"version" ascending:YES];
        [self.mutArrTotalmviFiles sortUsingDescriptors:@[sortDescriptor1]];
        
        intupdate = [responsedictionary[@"updates_count"]intValue];
        
        [UserDefaults setInteger:intupdate forKey:@"TOTALUPDATES"];
        
        [UserDefaults synchronize];
        
        [SharedDatabase Delete:@"Delete From UpdatesList where UpdateDownloaded = '0'"];
        [self insertUpdateCount:[responsedictionary[@"updates_count"] intValue]];
        
        for (int aIntCntI = 0; aIntCntI < [responsedictionary[@"update_list"] count]; aIntCntI++) {
            
            [self insertUpdateList:responsedictionary[@"update_list"][aIntCntI]];
        }
        
        //===================
        // Version 2.4 Changes
        //===================
        // updatesList3 response is changed, so now we get dictionary instead of array for "files" key...
        // ArrUpdatedownload used other places but that is not affecting download logic. (No need to change anything)
        
        for(int cnt=0; cnt<arr.count; cnt++)
        {
            NSMutableDictionary *dictfile = arr[cnt];
            
            // Version 2.5 change support for app upgrade with updates list...
            // We will get empty or no files for upgrade MVI...
            // Check "files" avaialble or not...
            if (dictfile[@"files"]) {
                NSDictionary *aDictFilesData = dictfile[@"files"];
                [self insertIntoUpdateFileList:aDictFilesData andVersionNumber:dictfile[@"version_number"]];
            }
        }
        
        MutArrUpList = [self getUpdateList];
        [tblViewUpdates reloadData];
        
        if (MutArrUpList.count > 0) {
            
        } else {
            
            lblNoupDates.text = @"There are no new updates available";
        }
        
        [self saveDBForInApp];
        
    }
    else
    {
        isUpdateDownloaded = TRUE;
        lblNoupDates.text = @"No more updates available.";
        lblNoupDates.hidden = FALSE;
    }
    
}

/*
 // ============ old working function before new codes for auto renew subscriptions ============
 -(void)checkUpdateInApp:(NSDictionary *)responsedictionary
 {
 // int intStatus = [[responsedictionary objectForKey:@"status"] intValue];
 
 int intStatusCode = [[responsedictionary objectForKey:@"status_code"] intValue];
 
 // For success StatusCode is 111...
 if ([[responsedictionary objectForKey:@"status"] intValue] == 1 && intStatusCode == 111) {
 
 if ([[responsedictionary objectForKey:@"updates_count"]intValue]>0) {
 
 // --------------------   In App DB ---------------
 
 NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
 
 NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
 
 int days;
 
 if ([UserDefaults  integerForKey:@"Plan"]==1) {
 days = 180;
 } else {
 days = 365;
 }
 NSDateFormatter *dtFrmt = [[NSDateFormatter alloc] init];
 
 [dtFrmt setDateFormat:@"dd/MM/yyyy"];
 
 NSString *aStrDa = [responsedictionary objectForKey:@"subscription_expiry"];
 NSString *aStrEndDt = [self strGetDate:aStrDa];
 
 NSString *strReceipts = [UserDefaults  objectForKey:@"Receipt"] ? [UserDefaults  objectForKey:@"Receipt"] : @"" ;
 
 //NSString *aStrEndDt = [NSString stringWithFormat:@"%@/%@/%@", [[aStrDa substringToIndex:6] substringToIndex:2],[[aStrDa substringFromIndex:4] substringToIndex:2],[aStrDa substringToIndex:4]];
 
 if ([aMutArrNames count] > 0) {
 
 NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update InAppTbl set  End_Date = \'%@\' where  ID='1'",aStrEndDt];
 [SharedDatabase Update:aStrUpdateSql];
 
 } else {
 
 NSString *aStrInsert = [NSString stringWithFormat:@"insert into  InAppTbl ('Receipt','Purchase_Date', 'End_Date', 'Created_Date', 'Plan', 'Kind') values (\'%@\',\'%@\', \'%@\', \'%@\', \'%d\', \'%@\')",strReceipts,[dtFrmt stringFromDate:[NSDate date]],aStrEndDt, [dtFrmt stringFromDate:[NSDate date]], days, @"InApp"];
 
 [SharedDatabase Insert:aStrInsert];
 }
 
 // -------------------------------------
 
 if(ArrUpdatedownload) {
 [self.ArrUpdatedownload removeAllObjects];
 }
 
 if(arrMviFile) {
 [self.arrMviFile removeAllObjects];
 }
 if(mutArrTotalmviFiles) {
 [mutArrTotalmviFiles removeAllObjects];
 }
 
 NSMutableArray *arr= [responsedictionary objectForKey:@"update_list"];
 
 int counter=1;
 for(int i=0;i<[arr count];i++) {
 
 NSString *strMVIFile = [[arr objectAtIndex:i]objectForKey:@"placement_determination_file"];
 NSString *astr = [[arr objectAtIndex:i]objectForKey:@"version_number"];
 
 NSMutableDictionary *dict =[[NSMutableDictionary alloc]init] ;
 NSMutableDictionary *mutDict = [[NSMutableDictionary alloc]init];
 [dict setValue:strMVIFile forKey:@"mviFile"];
 [mutDict setValue:astr forKey:@"version"];
 [self.arrMviFile addObject:dict];
 [mutArrTotalmviFiles addObject:mutDict];
 counter++;
 }
 
 NSSortDescriptor *sortDescriptor;
 sortDescriptor = [[NSSortDescriptor alloc] initWithKey:@"mviFile" ascending:YES];
 [self.arrMviFile sortUsingDescriptors:[NSArray arrayWithObject:sortDescriptor]];
 NSSortDescriptor *sortDescriptor1;
 sortDescriptor1 = [[NSSortDescriptor alloc] initWithKey:@"version" ascending:YES];
 [self.mutArrTotalmviFiles sortUsingDescriptors:[NSArray arrayWithObject:sortDescriptor1]];
 
 intupdate = [[responsedictionary objectForKey:@"updates_count"]intValue];
 
 [UserDefaults setInteger:intupdate forKey:@"TOTALUPDATES"];
 
 [UserDefaults synchronize];
 
 [SharedDatabase Delete:@"Delete From UpdatesList where UpdateDownloaded = '0'"];
 [self insertUpdateCount:[[responsedictionary objectForKey:@"updates_count"] intValue]];
 
 for (int aIntCntI = 0; aIntCntI < [[responsedictionary objectForKey:@"update_list"] count]; aIntCntI++) {
 
 [self insertUpdateList:[[responsedictionary objectForKey:@"update_list"] objectAtIndex:aIntCntI]];
 }
 
 
 //TODO: Only for testing - start
 //=============
 Old code...
 for(int cnt=0;cnt<[arr count];cnt++)
 {
 NSMutableDictionary *dictfile = [arr objectAtIndex:cnt];
 NSMutableArray *arrUpdates = [dictfile objectForKey:@"files"];
 for (int cnt_i = 0; cnt_i < [arrUpdates count] ; cnt_i ++)
 {
 NSMutableDictionary *replaceDict = [arrUpdates objectAtIndex:cnt_i];
 [replaceDict setValue:[dictfile objectForKey:@"version_number"] forKey:@"version_number"];
 [arrUpdates replaceObjectAtIndex:cnt_i withObject:replaceDict];
 
 }
 [self insertIntoUpdateTable:arrUpdates forDic:dictfile];
 [self.ArrUpdatedownload addObjectsFromArray:arrUpdates];
 }
 //TODO: Only for testing - end
 
 //===================
 // Version 2.4 Changes
 //===================
 // updatesList3 response is changed, so now we get dictionary instead of array for "files" key...
 // ArrUpdatedownload used other places but that is not affecting download logic. (No need to change anything)
 
 for(int cnt=0; cnt<[arr count]; cnt++)
 {
 NSMutableDictionary *dictfile = [arr objectAtIndex:cnt];
 
 // Version 2.5 change support for app upgrade with updates list...
 // We will get empty or no files for upgrade MVI...
 // Check "files" avaialble or not...
 if ([dictfile objectForKey:@"files"]) {
 NSDictionary *aDictFilesData = [dictfile objectForKey:@"files"];
 [self insertIntoUpdateFileList:aDictFilesData andVersionNumber:[dictfile objectForKey:@"version_number"]];
 }
 }
 
 MutArrUpList = [self getUpdateList];
 [tblViewUpdates reloadData];
 
 if ([MutArrUpList count] > 0) {
 
 } else {
 
 lblNoupDates.text = @"There are no new updates available";
 }
 
 [self saveDBForInApp];
 
 }
 else
 {
 isUpdateDownloaded = TRUE;
 lblNoupDates.text = @"No more updates available.";
 lblNoupDates.hidden = FALSE;
 
 }
 
 } else {
 isUpdateDownloaded = TRUE;
 lblNoupDates.text = @"No more updates available.";
 
 // Version 2.5 Changes...
 // Here StatusCodes can be 404, 405...
 NSString *aStrResponse = [responsedictionary objectForKey:@"message"];
 
 if (intStatusCode == 405) {
 
 // Membership inactive or expired...
 
 [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrResponse cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
 
 // Disable FullContent...
 [self disableFullContentAndShowBasicVersion];
 }];
 
 }
 else if (intStatusCode == 404) {
 
 // Display the error message...
 // Note : On click of OK Return to Purchase Dialgog but not feasible...
 [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrResponse cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
 }
 else {
 
 // Display the error message...
 [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrResponse cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
 }
 
 }
 [self setUpdateCount];
 }
 */

-(void)checkUpdate:(NSDictionary *)responsedictionary
{
    
    int intStatusCode = [responsedictionary[@"status_code"] intValue];
    
    // For success StatusCode is 111...
    if ([responsedictionary[@"status"] intValue] == 1 && intStatusCode == 111) {
        
        if ([responsedictionary[@"updates_count"]intValue]>0) {
            
            // Save new updated info and download data...
            [self saveNewUpdatedInfoAndDownloadUpdates:responsedictionary];
            
        }
        else
        {
            isUpdateDownloaded = TRUE;
            lblNoupDates.text = @"No more updates available.";
            lblNoupDates.hidden = FALSE;
            
        }
        
    }
    else {
        
        // Version 2.5 Changes...
        // Here StatusCodes can be 407, 424...
        NSString *aStrResponse = responsedictionary[@"message"];
        
        if (intStatusCode == 407) {
            
            // Membership inactive or expired...
            
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrResponse cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                
                // Disable FullContent...
                [self disableFullContentAndShowBasicVersion];
            }];
            
        }
        else if (intStatusCode == 424) {
            
            NSString *aStrMessage = [NSString stringWithFormat:@"%d - %@", intStatusCode, aStrResponse];
            // Display the error message and number...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrMessage cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        else {
            
            // Display the error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrResponse cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        
        
    }
    
    [self setUpdateCount];
    
}

- (void)saveNewUpdatedInfoAndDownloadUpdates:(NSDictionary *)responsedictionary {
    
    /* --------------------   In App DB ---------------*/
    
    NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
    NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
    
    
    NSDateFormatter *dtFrmt = [[NSDateFormatter alloc] init];
    dtFrmt.dateFormat = @"yyyy/MM/dd";
    
    NSString *aStrDa = responsedictionary[@"subscription_expiry"];
    NSString *aStrEndDt = [self strGetDate:aStrDa];
    
    //  NSString *aStrEndDt = [NSString stringWithFormat:@"%@/%@/%@", [[aStrDa substringToIndex:6] substringToIndex:2],[[aStrDa substringFromIndex:4] substringToIndex:2],[aStrDa substringToIndex:4]];
    
    if (aMutArrNames.count > 0)
    {
        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update InAppTbl set  End_Date = \'%@\' where  ID='1'",aStrEndDt];
        
        [SharedDatabase Update:aStrUpdateSql];
    }
    else
    {
        NSString *aStrInsert = [NSString stringWithFormat:@"insert into  InAppTbl ('Receipt', 'Purchase_Date', 'End_Date', 'Created_Date', 'Plan', 'Kind') values (\'%@\', \'%@\', \'%@\', \'%@\', \'%@\', \'%@\')", @"none", [dtFrmt stringFromDate:[NSDate date]], aStrEndDt, [dtFrmt stringFromDate:[NSDate date]], @"0", @"Subscribe"];
        
        [SharedDatabase Insert:aStrInsert];
    }
    
    /* -------------------------------------*/
    
    if(ArrUpdatedownload)
    {
        [self.ArrUpdatedownload removeAllObjects];
    }
    
    if(arrMviFile)
    {
        [self.arrMviFile removeAllObjects];
    }
    
    if(mutArrTotalmviFiles)
    {
        [mutArrTotalmviFiles removeAllObjects];
    }
    
    NSMutableArray *arr= responsedictionary[@"update_list"];
    
    int counter=1;
    for(int i=0;i<arr.count;i++)
    {
        
        NSString *strMVIFile = arr[i][@"placement_determination_file"];
        NSString *astr = arr[i][@"version_number"];
        
        NSMutableDictionary *dict =[[NSMutableDictionary alloc]init] ;
        NSMutableDictionary *mutDict = [[NSMutableDictionary alloc]init];
        [dict setValue:strMVIFile forKey:@"mviFile"];
        [mutDict setValue:astr forKey:@"version"];
        [self.arrMviFile addObject:dict];
        [mutArrTotalmviFiles addObject:mutDict];
        counter++;
    }
    
    NSSortDescriptor *sortDescriptor;
    sortDescriptor = [[NSSortDescriptor alloc] initWithKey:@"mviFile" ascending:YES];
    [self.arrMviFile sortUsingDescriptors:@[sortDescriptor]];
    NSSortDescriptor *sortDescriptor1;
    sortDescriptor1 = [[NSSortDescriptor alloc] initWithKey:@"version" ascending:YES];
    [self.mutArrTotalmviFiles sortUsingDescriptors:@[sortDescriptor1]];
    
    /* Old code...
     for(int cnt=0;cnt<[arr count];cnt++)
     {
     NSMutableDictionary *dictfile = [arr objectAtIndex:cnt];
     NSMutableArray *arrUpdates = [dictfile objectForKey:@"files"];
     for (int cnt_i = 0; cnt_i < [arrUpdates count] ; cnt_i ++)
     {
     NSMutableDictionary *replaceDict = [arrUpdates objectAtIndex:cnt_i];
     [replaceDict setValue:[dictfile objectForKey:@"version_number"] forKey:@"version_number"];
     [arrUpdates replaceObjectAtIndex:cnt_i withObject:replaceDict];
     
     }
     
     [self insertIntoUpdateTable:arrUpdates forDic:dictfile];
     
     [self.ArrUpdatedownload addObjectsFromArray:arrUpdates];
     
     }
     */
    
    //===================
    // Version 2.4 Changes
    //===================
    // updatesList3 response is changed, so now we get dictionary instead of array for "files" key...
    // ArrUpdatedownload used other places but that is not affecting download logic. (No need to change anything)
    
    for(int cnt=0; cnt<arr.count; cnt++)
    {
        NSMutableDictionary *dictfile = arr[cnt];
        
        // Version 2.5 change support for app upgrade with updates list...
        // We will get empty or no files for upgrade MVI...
        // Check "files" avaialble or not...
        if (dictfile[@"files"]) {
            NSDictionary *aDictFilesData = dictfile[@"files"];
            [self insertIntoUpdateFileList:aDictFilesData andVersionNumber:dictfile[@"version_number"]];
        }
    }
    
    intupdate = [responsedictionary[@"updates_count"]intValue];
    
    [UserDefaults setInteger:intupdate forKey:@"TOTALUPDATES"];
    
    [UserDefaults synchronize];
    
    [SharedDatabase Delete:@"Delete From UpdatesList where UpdateDownloaded = '0'"];
    [self insertUpdateCount:[responsedictionary[@"updates_count"] intValue]];
    
    for (int aIntCntI = 0; aIntCntI < [responsedictionary[@"update_list"] count]; aIntCntI++)
    {
        [self insertUpdateList:responsedictionary[@"update_list"][aIntCntI]];
    }
    MutArrUpList = [self getUpdateList];
    
    [tblViewUpdates reloadData];
    
    if (MutArrUpList.count > 0)
    {
        tblViewUpdates.hidden = NO;
        lblNoupDates.hidden = YES;
    }
    else
    {
        tblViewUpdates.hidden = YES;
        lblNoupDates.hidden = NO;
        lblNoupDates.text = @"No New Updates";
    }
    NSString *strSql = @"select count(UpdateUniqueId) from UpdatesList where UpdateDownloaded = 0";
    intupdate = [[Database shareDatabase] getCount:strSql];
    
}

-(void)deactiveUser:(NSDictionary *)responsedictionary
{
    NSString *aStrResponse = responsedictionary[@"message"];
    
    NSString *astrEmail = responsedictionary[@"user_info"][@"email"];
    
    int intStatus = [responsedictionary[@"status"] intValue];
    
    if(intStatus == 1)
    {
        isSubscribed = FALSE;
        [UserDefaults setObject:astrEmail forKey:@"email"];
        [viewRegistration removeFromSuperview];
        
        NSString *aStrUpdadte = [NSString stringWithFormat:@"update UserMaster set emailId = \'%@\', isRegistered = \'0\', isSubscribed = \'0\', SerialNumber = \'%@\' where userId = \'1\'",@" ",@" "];
        
        [SharedDatabase Update:aStrUpdadte];
        
    }
    else
    {
        NSString *msg = strAlertBiteFXSupport;
        if (aStrResponse.length > 0) {
            msg = aStrResponse;
        }
        // SCM - 2023-06-07 Error message now captures current action at time of error and status code returned by server.
        msg = [ErrorFormatter formatErrorMsgWithBaseString:msg errorType:DEACTIVATE_USER_ERROR serverReturnCode:intStatus];
        
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
            // tag 12
            if(self->viewRegistration)
            {
                [self->viewRegistration removeFromSuperview];
            }
            [self callRegisterwebService];
            self->isUpdateDownloaded = FALSE;
            self->intVideoCounter=self->intHtmlFileCounter=self->intJpgFileCounter=0;
            // isRegistrationSuccess = FALSE;
            self->isRegistered=FALSE;
            self->intUnregisterDone = 1;
            self->intUpdateCounter=self->intJpgUpdateCounter=self->inthtmlUpdateCounter=self->intMviCounter=0;
            
            if(self->intupdate>0)
            {
                self->imgMenuUpdates.hidden = NO;
                self->imgUpdatesAvailable.hidden = NO;
                self->lblUpdatesavailable.hidden = NO;
                self->lblUpdatesavailable.text  = [NSString stringWithFormat:@"%d",self->intupdate];
                self->lblMenuUpdates.hidden = NO;
                self->lblMenuUpdates.text = [NSString stringWithFormat:@"%d",self->intupdate];
                self->isUpdateDownloaded = FALSE;
            }
            [UserDefaults setBool:NO forKey:@"UpdateDownloaded"];
            [UserDefaults synchronize];
            [UserDefaults setBool:NO forKey:@"FullDownloaded"];
            [UserDefaults synchronize];
            [UserDefaults setBool:NO forKey:@"Registered"];
            [UserDefaults synchronize];
            self.txtFieldEmail.enabled = TRUE;
            self->txtFieldSerialnumber1.enabled = TRUE;
            
            NSString *sql = @"DELETE FROM LocalFileMaster";
            [SharedDatabase Delete:sql];
            [SharedDatabase Delete:@"DELETE FROM CollectionMaster"];
            [SharedDatabase Delete:@"DELETE FROM UpdateMaster"];
            [SharedDatabase Delete:@"DELETE FROM CollectionFilesMaster"];
            [SharedDatabase Delete:@"DELETE FROM UpdatesList"];
            
            [self startXMLParsing];
            [self insertPlayListCollection];
            [self getFirstCollectionVideoDetail];
            [self playVideoAtIndex:0];
            [self.m_queueplayer pause];
            [self setPlayPauseImageForNormalState];
            
            appDelegate.intSelectedScrNo = 1;
            appDelegate.intSelectedVideo  = 0;
            //Change to remove updates from the document-directory.
            [self RemoveFromDocumentDirectory];
        }];
        
    }
}

-(void)registerUser:(NSDictionary *)responsedictionary
{
    NSString *aStrResponse = responsedictionary[@"message"];
    NSString *astrEmail = responsedictionary[@"user_info"][@"email"];
    
    int intStatus = [responsedictionary[@"status"] intValue];
    int intStatusCode = [responsedictionary[@"status_code"] intValue];
    
    // For success StatusCode is 103...
    if(intStatus == 1 && intStatusCode == 103) {
        isRegistered=TRUE;
        FullorUpdate = 1;
        isSubscribed = TRUE;
        
        [UserDefaults setObject:astrEmail forKey:@"email"];
        
        NSString *msg = LOGIN_SUCCESS_MESSAGE;
        if (isNewVersionDataDownloading) {
            msg = UPDATE_SUCCESS_MESSAGE;
        }
        alertForConfirmation = [UIAlertController alertControllerWithTitle:ErrorTitle message:msg preferredStyle:UIAlertControllerStyleAlert];
        [self presentViewController:alertForConfirmation animated:YES completion:nil];
        
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            
            // Save and download data...
            [self saveRegisterUserInfoAndDownloadFullContent:responsedictionary];
            
        });
    }
    else
    {
        // Version 2.5 Changes...
        // Here StatusCodes can be 401, 402, 426...
        btnRegister.enabled = YES;
        NSString *msg = strAlertBiteFXSupport;
        if (aStrResponse.length > 0) {
            msg = aStrResponse;
        }
        // SCM - 2023-06-07 Error message now captures current action at time of error and status code returned by server.
        msg = [ErrorFormatter formatErrorMsgWithBaseString:msg errorType:REGISTER_USER_ERROR serverReturnCode:intStatusCode];
        
        //Tag = 2001
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
    
}
- (void)responsedidfail {
}

- (void)saveRegisterUserInfoAndDownloadFullContent:(NSDictionary *)responsedictionary {
    
    NSString *aStrUpdadte = [NSString stringWithFormat:@"update UserMaster set emailId = \'%@\', isRegistered = \'1\', isSubscribed = \'1\' , SerialNumber = \'%@\' where userId = \'1\'",self.strEmail,self.strSerialNumber];
    [SharedDatabase Update:aStrUpdadte];
    
    //===================
    // Version 2.4 Changes
    //===================
    // Login3 response is changed, so now we get dictionary instead of array...
    // self.mutArrFullVersion used other places but that is not affecting download logic. (No need to change anything)
    //        self.mutArrFullVersion = [[responsedictionary objectForKey:@"full_version"] retain];
    self.mutDictFullVersion = responsedictionary[@"full_version"];
    
    [self insertIntoDownloadFileList];
    
    self.aStrMviFile = responsedictionary[@"placement_determination_file"];
    [UserDefaults setObject:self.aStrMviFile forKey:MVI_FILE_URL];
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    //-----------------
    [UserDefaults setObject:responsedictionary[@"features_file"] forKey:FEATURES_MVI_FILE_URL];
    [UserDefaults synchronize];
    [self downloadFeaturesMVI];
    //===================
    
    isNewVersionDataDownloading = NO;
    int FullDownload = [UserDefaults boolForKey:@"FullDownloaded"];
    
    // Show alert on main thread...
    dispatch_async(dispatch_get_main_queue(), ^{
        
        // Remove viewRegistration...
        [self->viewRegistration removeFromSuperview];
        if(FullDownload)
        {
            [self dismissViewControllerAnimated:self->alertForConfirmation completion:nil];
            [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:@"You have already downloaded" cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        else
        {
            int aIntUpdate = [UserDefaults boolForKey:@"UpdateDownloaded"];
            if(aIntUpdate)
            {
                self->imgMenuUpdates.hidden = YES;
                self->imgUpdatesAvailable.hidden = YES;
                self->lblMenuUpdates.hidden = YES;
                self->lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",self->intupdate];
            }
            else
            {
                if(self->intupdate>0)
                {
                    self->imgMenuUpdates.hidden = NO;
                    self->imgUpdatesAvailable.hidden = NO;
                    self->lblMenuUpdates.hidden = NO;
                    self->lblMenuUpdates.hidden = NO;
                    
                    self->lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",self->intupdate];
                }
            }
            
            [UserDefaults setBool:YES forKey:@"Registered"];
            [UserDefaults synchronize];
            
            // In App ----
            self->userSubStat = Subscribed;
            [UserDefaults  setInteger:Subscribed forKey:@"UserStatusSubscription"];
            [UserDefaults synchronize];
            // -----------
            
            /* --------------------   In App DB ---------------*/
            NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
            
            NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
            
            NSDateFormatter *dtFrmt = [[NSDateFormatter alloc] init];
            
            dtFrmt.dateFormat = @"dd/MM/yyyy";
            
            NSString *aStrDa = responsedictionary[@"subscription_expiry"];
            NSString *aStrEndDt = [self strGetDate:aStrDa];
            
            if (aMutArrNames.count > 0)
            {
                NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update InAppTbl set  End_Date = \'%@\' where  ID='1'", aStrEndDt];
                [SharedDatabase Update:aStrUpdateSql];
            }
            else
            {
                NSString *aStrInsert = [NSString stringWithFormat:@"insert into  InAppTbl ('Receipt', 'Purchase_Date', 'End_Date', 'Created_Date', 'Plan', 'Kind') values (\'%@\', \'%@\', \'%@\', \'%@\', \'%@\', \'%@\')", @"none", [dtFrmt stringFromDate:[NSDate date]],aStrEndDt, [dtFrmt stringFromDate:[NSDate date]], @"0", @"Subscribe"];
                
                [SharedDatabase Insert:aStrInsert];
            }
            
            /* -------------------------------------*/
            
            [AppDelegate BitFXFull];
            
            AppDelegateobj.isFullversionDownloading = TRUE;
            self->arrFileDownloadList = [AppDelegate listOfRemainFileDownload];
            [self dismissViewControllerAnimated:self->alertForConfirmation completion:nil];
            if (self->arrFileDownloadList.count>0) {
                [self startFullVersionFileDownload];
            }
        }
        
    });
}

#pragma mark -
#pragma mark Action Events
- (void) AppleVersionUpdate {
    [self checkForRemainDownload];
    NSString *key = @"NewTableAdded";
    [UserDefaults  setBool:TRUE forKey:key];
}
- (void) checkForNewVersion {
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(AppleVersionUpdate) name:NewVersionUpdateSuccessfully object:nil];
    NSString *key = @"NewTableAdded";
    
    BOOL isVersionSet = [[NSUserDefaults standardUserDefaults ]boolForKey:key];
    
    if (isVersionSet == FALSE) {
        
        [AppDelegateobj createTableFoNewVersion];
        [AppDelegateobj callTestwebServiceRemainingVideos];
    }
}

- (void) insertIntoUpdateTable:(NSMutableArray *)arrFiles forDic:(NSMutableDictionary *)info {
    
    for (int i=0; i < arrFiles.count; i++) {
        
        NSMutableDictionary *dic = arrFiles[i];
        NSString *fileName = dic[@"file"];
        NSString *strSql = [NSString stringWithFormat:@"select count(serial) from FileUpdateList where FileName = '%@' and UpdateVersion = '%@'",fileName,info[@"version_number"]];
        NSInteger aIntCount = [[Database shareDatabase] getCount:strSql];
        if (aIntCount == 0) {
            NSString *strURL = [NSString stringWithFormat:@"%@/%@/%@",DownloadFileURL,info[@"version_number"],fileName];
            strSql = [NSString stringWithFormat:@"insert into FileUpdateList ('FileName','FileURL','IsDownloaded','UpdateVersion','isSkipFile') values ('%@','%@',0,'%@',0)",
                      fileName,strURL,info[@"version_number"]];
            //NSLog(@"strSql = %@",strSql);
            [[Database shareDatabase] Insert:strSql];
        }
        
        fileName = dic[@"html"];
        strSql = [NSString stringWithFormat:@"select count(serial) from FileUpdateList where FileName = '%@' and UpdateVersion = '%@'",fileName,info[@"version_number"]];
        aIntCount = [[Database shareDatabase] getCount:strSql];
        if (aIntCount == 0) {
            NSString *strURL = [NSString stringWithFormat:@"%@/%@/%@",DownloadFileURL,info[@"version_number"],fileName];
            strSql = [NSString stringWithFormat:@"insert into FileUpdateList ('FileName','FileURL','IsDownloaded','UpdateVersion','isSkipFile') values ('%@','%@',0,'%@',0)",
                      fileName,strURL,info[@"version_number"]];
            //NSLog(@"strSql = %@",strSql);
            [[Database shareDatabase] Insert:strSql];
        }
        
        fileName = dic[@"img"];
        strSql = [NSString stringWithFormat:@"select count(serial) from FileUpdateList where FileName = '%@' and UpdateVersion = '%@'",fileName,info[@"version_number"]];
        aIntCount = [[Database shareDatabase] getCount:strSql];
        if (aIntCount == 0) {
            NSString *strURL = [NSString stringWithFormat:@"%@/%@/%@",DownloadFileURL,info[@"version_number"],fileName];
            strSql = [NSString stringWithFormat:@"insert into FileUpdateList ('FileName','FileURL','IsDownloaded','UpdateVersion','isSkipFile') values ('%@','%@',0,'%@',0)",
                      fileName,strURL,info[@"version_number"]];
            
            [[Database shareDatabase] Insert:strSql];
        }
    }
    
}

- (void)insertIntoUpdateFileList:(NSDictionary *)dictFilesData andVersionNumber:(NSString *)strVersion {
    
    //-----------------
    //----Movie--------
    if (dictFilesData[@"movie"]) {
        
        NSArray *aArrMovies = dictFilesData[@"movie"];
        [self insertFileUpdateListInDatabase:aArrMovies andVersionNumber:strVersion];
    }
    
    //-----------------
    //----MovieInfo--------
    if (dictFilesData[@"movieinfo"]) {
        
        NSArray *aArrMoviesInfo = dictFilesData[@"movieinfo"];
        [self insertFileUpdateListInDatabase:aArrMoviesInfo andVersionNumber:strVersion];
    }
    
    //-----------------
    //----MovieThumb--------
    if (dictFilesData[@"moviethumb"]) {
        
        NSArray *aArrMoviesThumb = dictFilesData[@"moviethumb"];
        [self insertFileUpdateListInDatabase:aArrMoviesThumb andVersionNumber:strVersion];
    }
    
    //-----------------
    //----Image--------
    if (dictFilesData[@"image"]) {
        
        NSArray *aArrImages = dictFilesData[@"image"];
        [self insertFileUpdateListInDatabase:aArrImages andVersionNumber:strVersion];
    }
    
    //-----------------
    //----ImageThumb--------
    if (dictFilesData[@"imagethumb"]) {
        
        NSArray *aArrImagesThumb = dictFilesData[@"imagethumb"];
        [self insertFileUpdateListInDatabase:aArrImagesThumb andVersionNumber:strVersion];
    }
    
    //-----------------
    //----PresentationInfo--------
    if (dictFilesData[@"presentationinfo"]) {
        
        NSArray *aArrPresentationInfo = dictFilesData[@"presentationinfo"];
        [self insertFileUpdateListInDatabase:aArrPresentationInfo andVersionNumber:strVersion];
    }
    
}

- (void)insertFileUpdateListInDatabase:(NSArray *)arrData andVersionNumber:(NSString *)strVersion {
    
    for (int i=0; i < arrData.count; i++) {
        
        // Insert file url...
        NSString *aStrFileUrl = arrData[i];
        
        // Remove extra backslash in file path...
        aStrFileUrl = [aStrFileUrl stringByReplacingOccurrencesOfString:@"\\" withString:@"/"];
        
        NSString *aStrFileName = aStrFileUrl.lastPathComponent;
        
        NSString *strSql = [NSString stringWithFormat:@"select count(serial) from FileUpdateList where FileName = '%@' and UpdateVersion = '%@'", aStrFileName, strVersion];
        NSInteger aIntCount = [[Database shareDatabase] getCount:strSql];
        
        if (aIntCount == 0) {
            
            NSString *fileURL = [NSString stringWithFormat:@"%@/%@%@",DownloadFileURL, strVersion, aStrFileUrl];
            strSql = [NSString stringWithFormat:@"insert into FileUpdateList ('FileName','FileURL','IsDownloaded','UpdateVersion','isSkipFile') values ('%@','%@',0,'%@',0)", aStrFileName, fileURL, strVersion];
            [[Database shareDatabase] Insert:strSql];
        }
        
    }
    
}

- (IBAction)btnCheckForRamain:(id)sender {
    
    NSMutableArray *arrAllFile = [NSMutableArray array];
    
    NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
    [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
    
    if (arrAllFile.count > 0) {
        
        AppDelegateobj.isFullversionDownloading = TRUE;
        AppDelegateobj.isUpdateDownloading = FALSE;
        [self checkForRemainDownload];
        return;
    }
    
    NSString *strSql = [NSString stringWithFormat:@"select * from FileUpdateList where IsDownloaded = 0"];
    [arrAllFile addObjectsFromArray:[[Database shareDatabase] getAllDataForQuery:strSql]];
    
    if (arrAllFile.count > 0) {
        
        AppDelegateobj.isUpdateDownloading = TRUE;
        AppDelegateobj.isFullversionDownloading = FALSE;
        [self checkForRemainDownload];
    }
    
}

-(void)btnRemainDownLoadYesClicked
{
    appDelegate.isDownloadManually = NO;
    
    [self NoInternetConnectionShowAlert];
    
    self->intVideoCounter = 0;
    if (AppDelegateobj.isFullversionDownloading) {
        [UserDefaults setBool:YES forKey:@"Registered"];
        [UserDefaults synchronize];
        
        [self startFullVersionFileDownload];
    } else if (AppDelegateobj.isUpdateDownloading) {
        [self startUpdateDownloading];
    } else {
        [UserDefaults setBool:YES forKey:@"Registered"];
        [UserDefaults synchronize];
        
        [self startFullVersionFileDownload];
    }
}

-(void)openAlertForRemainDownload
{
    // tag REMAIN_DOWNLOAD
    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"It looks like the download was aborted or a few files could not be downloaded correctly. Would you like to continue the download?" cancelButtonTitle:@"NO" destructiveButtonTitle:nil otherButtonTitles:@[@"YES"] tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
        [self dismissViewControllerAnimated:YES completion:nil];
        if(buttonIndex == controller.cancelButtonIndex)
        {
            self->isUpdateInterrupted = 1;
            [self->viewDownloadingData removeFromSuperview];
        }
        else
        {
            [self btnRemainDownLoadYesClicked];
        }
    }];
}

- (BOOL) checkForRemainDownloadOnNetwork {
    
    arrFileDownloadList = [AppDelegate listOfRemainFileDownload];
    
    if (arrFileDownloadList.count > 0) {
        BOOL isUpdateBtnClicked = [UserDefaults  boolForKey:@"isUpdateBtnClicked"];
        if((!appDelegate.isFullversionDownloading && isUpdateBtnClicked) || appDelegate.isFullversionDownloading)
        {
            [self openAlertForRemainDownload];
        }
        return TRUE;
        
    } else {
        
        return FALSE;
    }
}

- (BOOL) checkForRemainDownload {
    
    arrFileDownloadList = [AppDelegate listOfRemainFileDownload];
    
    if (arrFileDownloadList.count > 0) {
        
        if (AppDelegateobj.isUpdateDownloading) {
            
            if (isUpdateInterrupted == 1) {
                
                btnRemainDownload.hidden = NO;
            } else {
                btnRemainDownload.hidden = YES;
            }
        } else {
            
            btnRemainDownload.hidden = NO;
        }
        
        if(!appDelegate.isFullversionDownloading)
        {
            NSLog(@"full version");
        }
        if ([UserDefaults  boolForKey:@"isUpdateBtnClicked"])
        {
            NSLog(@"update clicked");
        }
        BOOL isUpdateBtnClicked = [UserDefaults  boolForKey:@"isUpdateBtnClicked"];
        if((!appDelegate.isFullversionDownloading && isUpdateBtnClicked) || appDelegate.isFullversionDownloading)
        {
            [self openAlertForRemainDownload];
        }
        
        return TRUE;
        
    } else {
        btnRemainDownload.hidden = YES;
        return FALSE;
    }
}


- (void) insertIntoDownloadFileList {
    
    //===================
    // Version 3.1 Changes
    //===================
    // Feature MVI file changes.
    //-----------------
    //----FeatureSet Images--------
    NSArray *aArrImagesFeatureSet = (self.mutDictFullVersion)[@"featureset"];
    [self insertFileListInDatabase:aArrImagesFeatureSet];
    //===================
    
    // Old file download working code
    //-----------------
    //----Movie--------
    NSArray *aArrMovies = (self.mutDictFullVersion)[@"movie"];
    [self insertFileListInDatabase:aArrMovies];
    
    //-----------------
    //----MovieInfo--------
    NSArray *aArrMoviesInfo = (self.mutDictFullVersion)[@"movieinfo"];
    [self insertFileListInDatabase:aArrMoviesInfo];
    
    //-----------------
    //----MovieThumb--------
    NSArray *aArrMoviesThumb = (self.mutDictFullVersion)[@"moviethumb"];
    [self insertFileListInDatabase:aArrMoviesThumb];
    
    //-----------------
    //----Image--------
    NSArray *aArrImages = (self.mutDictFullVersion)[@"image"];
    [self insertFileListInDatabase:aArrImages];
    
    //-----------------
    //----ImageThumb--------
    NSArray *aArrImagesThumb = (self.mutDictFullVersion)[@"imagethumb"];
    [self insertFileListInDatabase:aArrImagesThumb];
    
    //-----------------
    //----PresentationInfo--------
    NSArray *aArrPresentationInfo = (self.mutDictFullVersion)[@"presentationinfo"];
    [self insertFileListInDatabase:aArrPresentationInfo];
    [UserDefaults setBool:YES forKey:IS_NEW_DATA_DOWNLOADED_FOR_VERSION_24];
}

- (void)insertFileListInDatabase:(NSArray *)arrData {
    
    for (int i=0; i < arrData.count; i++) {
        
        // Insert file url...
        NSString *aStrFileUrl = arrData[i];
        
        // Remove extra backslash in file path...
        aStrFileUrl = [aStrFileUrl stringByReplacingOccurrencesOfString:@"\\" withString:@"/"];
        
        // Remove space in file path...
        //        aStrFileUrl = [aStrFileUrl stringByReplacingOccurrencesOfString:@" " withString:@""];
        
        NSString *fileURL = [NSString stringWithFormat:@"%@%@",DownloadFileURL, aStrFileUrl];
        NSString *sql = [NSString stringWithFormat:@"insert into FileDownloadList ('FileURL','IsDownloaded','isSkipFile') values ('%@',0,0)", fileURL];
        [SharedDatabase Insert:sql];
        
    }
    
}
- (void)startFullVersionFileDownload{
    
    FullorUpdate = 1;
    UpdateCompleted = FALSE;
    appDelegate.isUpdatedMVIfile = FALSE;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:YES];
    
    if(viewDownloadingData)
    {
        [viewDownloadingData removeFromSuperview];
    }
    
    if(FullorUpdate==0) {
        
        AppDelegateobj.isUpdateDownloading = TRUE;
        AppDelegateobj.isFullversionDownloading = FALSE;
        lblThanksRegistration.hidden = YES;
        lblfullorupdate.text = defineDownloadingUpdates;
        
        lblDownloadMsg.text = @"This may take a while, depending on the speed of your internet connection.\nDO NOT exit the BiteFX app during download. \n\nIf the download is interrupted or it is detected that some files did not download correctly, you will have two options to resume downloading the missing items:\n1. Respond 'Yes' to a message BiteFX will display on start-up if it detects the download was interrupted.\n2. Tapping the 'Resume Download' button that is displayed in the BiteFX button bar when there are more files to download.";
        
    } else {
        
        AppDelegateobj.isUpdateDownloading = FALSE;
        AppDelegateobj.isFullversionDownloading = TRUE;
        lblfullorupdate.text = @"Downloading full content...";
        lblThanksRegistration.hidden = FALSE;
        lblThanksRegistration.text = @"Welcome BiteFX Subscriber"; //CR Change @"Thank you for registering with BiteFX";
        
        
        lblDownloadMsg.text=[NSString stringWithFormat:@" \t\tYour BiteFX content is being downloaded. \n\n\tThis may take a while depending on the speed of your Internet connection.\n\n\tDO NOT exit the BiteFX app during download. \n\nIf the download is interrupted or it is detected that some files did not download correctly, you will have two options to resume downloading the missing items:\n1. Respond 'Yes' to a message BiteFX will display on start-up if it detects the download was interrupted.\n2. Tapping the 'Resume Download' button that is displayed in the BiteFX button bar when there are more files to download."];
        
    }
    if(viewUpdates.superview)
    {
        [viewUpdates removeFromSuperview];
    }
    [self.view addSubview:viewDownloadingData];
    
    [self startFileDownloadingForIndex:intVideoCounter];
    
}

- (void) startFileDownloadingForIndex:(NSInteger)index {
    
    NSLog(@"Inside startFileDownloadingForIndex %ld", (long)index);
    if (index >= arrFileDownloadList.count) {
        
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        FullorUpdate = -1;
        appDelegate.isUpdatedMVIfile = FALSE;
        AppDelegateobj.isFullversionDownloading = FALSE;
        self.aStrMviFile = [UserDefaults  objectForKey:MVI_FILE_URL];
        
        // Version 2.4 change
        // Hide btnRemainDownload after Full Download...
        isUpdateInterrupted = 0;
        arrFileDownloadList = [AppDelegate listOfRemainFileDownload];
        
        if (arrFileDownloadList.count > 0) {
            
            if (AppDelegateobj.isUpdateDownloading) {
                
                if (isUpdateInterrupted == 1) {
                    btnRemainDownload.hidden = NO;
                } else {
                    btnRemainDownload.hidden = YES;
                }
            } else {
                
                if (AppDelegateobj.isFullversionDownloading) {
                    
                    btnRemainDownload.hidden = NO;
                }
                else
                {
                    btnRemainDownload.hidden = YES;
                }
                
            }
        } else {
            
            btnRemainDownload.hidden = YES;
        }
        
        // First delete basic version data...
        [self deleteOldData];
        [self DownloadFullVersionMVI];
        return;
    }
    
    NSString *strURL = arrFileDownloadList[index];
    
    [self setNoOfVideoCompleted:[NSString stringWithFormat:@"Downloading %ld of %ld file(s)...",(long)index+1,(unsigned long)arrFileDownloadList.count]];
    
    progressBar.progress = 0;
    btnRemainDownload.hidden = NO;
    
    [self dataDownloadAtPercent:0];
    [self.downloadManager cleanup];
    self.downloadManager.delegate = self;
    //[self.downloadManager download:strURL];
    
    NSString *downloadFolder = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"BiteFXiPadFull/"];
    NSString *downloadFilename = [downloadFolder stringByAppendingPathComponent:strURL.lastPathComponent];
    
    // Remove space in url...
    //    NSString *aStrEscapedUrl = [strURL stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    NSString *aStrEscapedUrl = [NSString stringWithFormat:@"%s",strURL.UTF8String];
    NSURL *url1 = [NSURL URLWithString:aStrEscapedUrl];
    NSLog(@"Download url: %@",url1);
    [self.downloadManager download:strURL path:downloadFilename withURL:url1];
}

-(void)setNoOfVideoCompleted:(NSString*)str{
    lblNoofVideoCompleted.text = str;
}

- (void) startUpdateDownloading {
    
    FullorUpdate = 0;
    UpdateCompleted = FALSE;
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:YES];
    
    if(viewDownloadingData) {
        
        [viewDownloadingData removeFromSuperview];
    }
    
    lblThanksRegistration.hidden = FALSE;
    lblThanksRegistration.text = @"Welcome BiteFX Subscriber";
    lblDownloadMsg.text=[NSString stringWithFormat:@" \t\tYour BiteFX content is being downloaded. \n\n\tThis may take a while depending on the speed of your Internet connection.\n\n\tDO NOT exit the BiteFX app during download. \n\nIf the download is interrupted or it is detected that some files did not download correctly, you will have two options to resume downloading the missing items:\n1. Respond 'Yes' to a message BiteFX will display on start-up if it detects the download was interrupted.\n2. Tapping the 'Resume Download' button that is displayed in the BiteFX button bar when there are more files to download."];
    
    if(FullorUpdate==0) {
        AppDelegateobj.isUpdateDownloading = TRUE;
        AppDelegateobj.isFullversionDownloading = FALSE;
        lblfullorupdate.text = defineDownloadingUpdates;
    } else {
        AppDelegateobj.isUpdateDownloading = FALSE;
        AppDelegateobj.isFullversionDownloading = TRUE;
        lblfullorupdate.text = @"Downloading full content...";
    }
    
    if(viewUpdates.superview) {
        [viewUpdates removeFromSuperview];
    }
    
    [self.view addSubview:viewDownloadingData];
    [self startUpdateFileDownloadingForIndex:intVideoCounter];
}

- (void) startUpdateFileDownloadingForIndex:(NSInteger)index {
    
    if (index >= arrFileDownloadList.count) {
        
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        
        NSMutableArray *aTempSkipCountArray = [[Database shareDatabase]getAllDataForQuery:@"select * from FileUpdateList where isSkipFile = '1' and IsDownloaded = '0'"];
        NSString *aTempString;
        if(aTempSkipCountArray.count == 0)
        {
            aTempString = defineFileDownloadSuccessMessage;
        }
        else
        {
            aTempString = [NSString stringWithFormat:@"All but %lu files were downloaded successfully",(unsigned long)aTempSkipCountArray.count];
        }
        lblProgresspercent.text = aTempString;
        progressBar.hidden = YES;
        UpdateCompleted = TRUE;
        AppDelegateobj.isUpdateDownloading = FALSE;
        AppDelegateobj.isFullversionDownloading = TRUE;
        isUpdateInterrupted = 0;
        
        arrFileDownloadList = [AppDelegate listOfRemainFileDownload];
        
        if (arrFileDownloadList.count > 0) {
            
            if (AppDelegateobj.isUpdateDownloading) {
                
                if (isUpdateInterrupted == 1) {
                    btnRemainDownload.hidden = NO;
                } else {
                    btnRemainDownload.hidden = YES;
                }
            } else {
                
                btnRemainDownload.hidden = NO;
            }
        } else {
            btnRemainDownload.hidden = YES;
        }
        
        [self CheckingUpdates];
        NSLog(@"All Update Downloaded ");
        
        // For updates in MVI files...(Which does not add new files but update sequence or delete any file)
        [self startXMLParsingForUpdatedMVI];
        
        NSString *strSql = @"select count(UpdateUniqueId) from UpdatesList where UpdateDownloaded = 0";
        intupdate = [[Database shareDatabase] getCount:strSql];
        
        imgMenuUpdates.hidden = YES;
        lblMenuUpdates.hidden = YES;
        lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
        
        imgUpdatesAvailable.hidden = YES;
        lblUpdatesavailable.hidden  = YES;
        lblUpdatesavailable.text =[NSString stringWithFormat:@"%d",intupdate];
        lblNoofVideoCompleted.text = @"";
        return;
    }
    
    NSString *strURL = arrFileDownloadList[index][@"FileURL"];
    
    [self setNoOfVideoCompleted:[NSString stringWithFormat:@"Downloading %ld of %ld file(s)...", (long)index+1,(long)arrFileDownloadList.count]];
    progressBar.progress = 0;
    
    if (arrFileDownloadList.count == 0) {
        btnRemainDownload.hidden = YES;
        
    } else {
        
        if (AppDelegateobj.isUpdateDownloading) {
            
            if (isUpdateInterrupted == 1) {
                btnRemainDownload.hidden = NO;
            } else {
                btnRemainDownload.hidden = YES;
            }
        } else {
            
            btnRemainDownload.hidden = NO;
        }
    }
    
    [self dataDownloadAtPercent:0];
    [self.downloadManager cleanup];
    self.downloadManager.delegate = self;
    
    NSString *downloadFolder = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"Update0001/"];
    NSString *downloadFilename = [downloadFolder stringByAppendingPathComponent:strURL.lastPathComponent];
    
    // Remove space in url...
    //    NSString *aStrEscapedUrl = [strURL stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    NSString *aStrEscapedUrl = [NSString stringWithFormat:@"%s",strURL.UTF8String];
    
    NSURL *url1 = [NSURL URLWithString:aStrEscapedUrl];
    NSLog(@"Download url: %@",url1);
    [self.downloadManager download:strURL path:downloadFilename withURL:url1];
}
- (void)StartDownloading {
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:YES];
    
    if(viewDownloadingData) {
        [viewDownloadingData removeFromSuperview];
    }
    if(FullorUpdate==0) {
        
        AppDelegateobj.isUpdateDownloading = TRUE;
        AppDelegateobj.isFullversionDownloading = FALSE;
        lblThanksRegistration.hidden = YES;
        lblfullorupdate.text = defineDownloadingUpdates;
        
        lblDownloadMsg.text = @"This may take a while, depending on the speed of your internet connection.\nDO NOT exit the BiteFX app during download. \n\nIf the download is interrupted or it is detected that some files did not download correctly, you will have two options to resume downloading the missing items:\n1. Respond 'Yes' to a message BiteFX will display on start-up if it detects the download was interrupted.\n2. Tapping the 'Resume Download' button that is displayed in the BiteFX button bar when there are more files to download.";
        
    } else {
        AppDelegateobj.isUpdateDownloading = FALSE;
        AppDelegateobj.isFullversionDownloading = TRUE;
        lblfullorupdate.text = @"Downloading full content...";
        lblThanksRegistration.hidden = FALSE;
        lblThanksRegistration.text = @"Welcome BiteFX Subscriber"; //CR Change @"Thank you for registering with BiteFX";
        
        lblDownloadMsg.text = [NSString stringWithFormat:@" \t\tYour BiteFX content is being downloaded. \n\n\tThis may take a while depending on the speed of your Internet connection.\n\n\tDO NOT exit the BiteFX app during download. \n\nIf the download is interrupted or it is detected that some files did not download correctly, you will have two options to resume downloading the missing items:\n1. Respond 'Yes' to a message BiteFX will display on start-up if it detects the download was interrupted.\n2. Tapping the 'Resume Download' button that is displayed in the BiteFX button bar when there are more files to download."];
        
    }
    if(viewUpdates.superview) {
        [viewUpdates removeFromSuperview];
    }
    [self.view addSubview:viewDownloadingData];
    
    lblProgresspercent.text = @"";
    
    if(FullorUpdate==1) {
        intVideoCounter = 0;
        [self downLoadFile];
    }
    else {
        [self downLoadFile];
    }
}
- (void) saveData:(NSData *)data forURL:(NSString *)strURL{
    
#pragma mark --------------------------------
#pragma mark Version 2.4 Folder Structure Changes
    
    //    NSRange range1 = [strURL rangeOfString:@"version3"];
    //    NSRange rangeToSubString = NSMakeRange(range1.location + range1.length, strURL.length);
    
    //    NSString *aStrPath = [strURL componentsSeparatedByString:@"version3"][1];
    NSString *aStrPath = [strURL componentsSeparatedByString:CURRENT_VERSION][1];
    
    // Remove space in file path...
    aStrPath = [aStrPath stringByReplacingOccurrencesOfString:@" " withString:@""];
    
    //    NSString *dir_path= [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"BiteFXiPadFull/"];
    NSString *filePath = [NSString stringWithFormat:@"%@/%@%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, aStrPath];
    
    //    NSString *fileName = [self getFileNameFromURL:strURL];
    //    NSString *filePath = [NSString stringWithFormat:@"%@/%@",dir_path,fileName];
    
    if( [data writeToFile:filePath atomically:YES]) {
        [self setAttribDonotMarkiCloudSetting:filePath];
    } else {
        //NSLog(@"file can not save for %@",strURL);
    }
    
    NSString *sql = @"";
    sql = [NSString stringWithFormat:@"update FileDownloadList set IsDownloaded = 1, isSkipFile = 0 where FileURL = '%@'",strURL];
    intVideoCounter++;
    [[Database shareDatabase] Update:sql];
    
    if(appDelegate.isSkipVideoFullVerison)
    {
        sql = [NSString stringWithFormat:@"update FileDownloadList set isSkipFile = '0',IsDownloaded = '1' where FileURL = '%@'",strURL];
        [[Database shareDatabase] Update:sql];
        intSkipVideoCount++;
        [self startSkipFullVerisonFileDownload:intSkipVideoCount];
        
        FullorUpdate = 1;
        return;
    }
    self.downloadManager.isDownloading = FALSE;
    [self startFileDownloadingForIndex:intVideoCounter];
}

- (void) saveUpdateData:(NSData *)data forURL:(NSString *)strURL {
    
    NSString *dir_path= [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"Update0001/"];
    NSString *fileName = [self getFileNameFromURL:strURL];
    NSString *filePath = [NSString stringWithFormat:@"%@/%@",dir_path,fileName];
    
    if( [data writeToFile:filePath atomically:YES]) {
        
        [self setAttribDonotMarkiCloudSetting:filePath];
        
    } else {
    }
    
    
    NSString *sql = @"";
    sql = [NSString stringWithFormat:@"update FileUpdateList set isSkipFile = 0,IsDownloaded = 1 where FileURL = '%@'",strURL];
    [[Database shareDatabase] Update:sql];
    
    NSString *sqlFileCount = [NSString stringWithFormat:@"select count (fileTitle) from LocalFileMaster where fileTitle = '%@'",fileName];
    
    NSInteger fileCount = [[Database shareDatabase] getCount:sqlFileCount];
    
    if (fileCount == 1) {
        NSString *thumbnailPath = [NSString stringWithFormat:@"Update0001/%@.jpg",[fileName substringToIndex:fileName.length-4]];
        NSLog(@"%@",thumbnailPath);
        
        NSString *infoFilePath = [NSString stringWithFormat:@"Update0001/%@.html",[fileName substringToIndex:fileName.length-4]];
        NSLog(@"%@",infoFilePath);
        
        if([thumbnailPath containsString:@".jpg"]) {
            // if we get same name for movie update & same name found for image "Leaf Guage" then image should not update
            NSString *newVidNm = [thumbnailPath stringByReplacingOccurrencesOfString:@"jpg" withString:VIDEO_EXTENSION];
            newVidNm = [newVidNm stringByReplacingOccurrencesOfString:@"Update0001/" withString:@""];
            NSString *vidFileUpdatedPath = [NSString stringWithFormat:@"Update0001/%@",newVidNm];
            NSString *sql = [NSString stringWithFormat:@"select count (fileTitle) from LocalFileMaster where fileTitle = '%@' and filePath = '%@'",newVidNm,vidFileUpdatedPath];
            NSInteger vidCnt = [[Database shareDatabase] getCount:sql];
            
            if(vidCnt == 0) {
                NSString *aFilePath = [NSString stringWithFormat:@"Update0001/%@",fileName];
                NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '0', infoFilePath = \'%@\',thumbnailPath = \'%@\', filePath = \'%@\' where  fileTitle=\'%@\'",infoFilePath,thumbnailPath,aFilePath,fileName];
                [SharedDatabase Update:aStrUpdateSql];
            }
            
        }
    }
    
    if(!appDelegate.isSkipVideoUpdateVerison){
        NSMutableDictionary *dicInfo = arrFileDownloadList[intVideoCounter];
        NSInteger intVersion =[dicInfo[@"UpdateVersion"] intValue];
        if ([self isUpdateDownloadingCompleted:intVersion]) {
            NSLog(@"version is completed %ld",(long)intVersion);
            appDelegate.isUpdatedMVIfile = TRUE;
            [self updateIsCompletedForVersion:intVersion];
        }else{
            NSString *strSql = [NSString stringWithFormat:@"select * from UpdatesList where UpdateVersion = %ld",(long)intVersion];
            
            NSArray *arrGetData=  [[Database shareDatabase] getFirstColumnForQuery:strSql];
            if(arrGetData.count>0)
            {
                NSLog(@"update version set for no %ld No Errors",(long)intVersion);
                strSql = [NSString stringWithFormat:@"update UpdatesList set UpdateDownloaded = 1 where UpdateVersion = %ld",(long)intVersion];
                [[Database shareDatabase] Update:strSql];
            }
        }
    }
    
    intVideoCounter++;
    if(appDelegate.isSkipVideoUpdateVerison)
    {
        
        if(mutArrSkipDownloadList.count > 0)
        {
            NSMutableDictionary *dicInfo = mutArrSkipDownloadList[intSkipVideoCount];
            NSInteger intVersion =[dicInfo[@"UpdateVersion"] intValue];
            if ([self isUpdateDownloadingCompleted:intVersion]) {
                NSLog(@"version is completed %ld",(long)intVersion);
                appDelegate.isUpdatedMVIfile = TRUE;
                [self updateIsCompletedForVersion:intVersion];
            }
        }
        
        sql = [NSString stringWithFormat:@"update FileUpdateList set isSkipFile = 0,IsDownloaded = 1 where FileURL = '%@'",strURL];
        [[Database shareDatabase] Update:sql];
        
        intSkipVideoCount++;
        isManageArrayList = NO;
        [self startSkipUpdateVersionFileDownload:intSkipVideoCount];
        FullorUpdate = 0;
        return;
    }
    
    
    self.downloadManager.isDownloading = FALSE;
    [self startUpdateFileDownloadingForIndex:intVideoCounter];
    
}

- (BOOL) isUpdateDownloadingCompleted:(NSInteger )intVersion {
    
    NSString *strSql = [NSString stringWithFormat:@"select count(serial) from FileUpdateList where IsDownloaded = 0 and UpdateVersion = %ld",(long)intVersion];
    NSInteger intUpdateCount = [[Database shareDatabase] getCount:strSql];
    return (intUpdateCount > 0) ? FALSE : TRUE;
}

- (void)startXMLParsingForUpdatedMVI {
    
#pragma mark --------------------------------
#pragma mark Version 2.4 Changes
    
    // For updates in MVI files which does not add new files but update sequence or delete any file we need to call "updateIsCompletedForVersion" for XML parsing ...
    NSString *strSql = [NSString stringWithFormat:@"select * from UpdatesList where UpdateDownloaded = 0"];
    
    NSArray *arrUpdatesData=  [[Database shareDatabase] getAllDataForQuery:strSql];
    if(arrUpdatesData.count>0)
    {
        for (int index = 0; index < arrUpdatesData.count; index++) {
            
            NSMutableDictionary *aDictInfo = arrUpdatesData[index];
            NSInteger intVersion =[aDictInfo[@"UpdateVersion"] intValue];
            
            NSLog(@"version is completed %ld",(long)intVersion);
            appDelegate.isUpdatedMVIfile = TRUE;
            [self updateIsCompletedForVersion:intVersion];
            
        }
    }
    
    // Version 2.5 change...
    // Issue resolved - Download Alert for Updates will only show if update button is clicled...
    // All updates downloaded...
    [UserDefaults  setBool:NO forKey:@"isUpdateBtnClicked"];
    [UserDefaults  synchronize];
    
}

- (void) updateIsCompletedForVersion:(NSInteger)intVersion {
    
    NSString *strSql = [NSString stringWithFormat:@"select * from UpdatesList where UpdateVersion = %ld",(long)intVersion];
    NSMutableDictionary *dicInfo = [[Database shareDatabase] getFirstRowForQuery:strSql];
    NSString *strMVIUrl = [NSString stringWithFormat:@"%@/%ld/%@",DownloadFileURL,(long)intVersion,dicInfo[@"xmlfilePath"]];
    
    //    NSURL *url = [NSURL URLWithString:[strMVIUrl stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]];
    NSURL *url = [NSURL URLWithString:[NSString stringWithFormat:@"%s",strMVIUrl.UTF8String]];
    NSData *xmlData1 = [NSData dataWithContentsOfURL:url];
    
    // Testing purpose...
    //    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    //    NSString *documentsDir = [paths objectAtIndex:0];
    //
    //    NSString *dir_path= [documentsDir stringByAppendingPathComponent:@"BiteFXiPadFull/TestSeq.MVI"];
    //
    //    // NSData* xmldata=[NSData dataWithContentsOfFile:dir_path];
    //    xmlData1 =[NSData dataWithContentsOfFile:dir_path];
    //    appDelegate.isUpdatedMVIfile = YES;
    //    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"FullDownloaded"];
    //    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"UpdateDownloaded"];
    //    [[NSUserDefaults standardUserDefaults] synchronize];
    
    NSString* myString;
    myString = [[NSString alloc] initWithData:xmlData1 encoding:NSASCIIStringEncoding];
    
    NSLog(@"Data to String ==== %@",myString);
    
    myString = [myString stringByReplacingOccurrencesOfString:@"@@-" withString:@""];
    myString = [myString stringByReplacingOccurrencesOfString:@"@@=" withString:@""];
    myString = [myString stringByReplacingOccurrencesOfString:@"@@+" withString:@""];
    myString = [myString stringByReplacingOccurrencesOfString:@"@@x" withString:@""];
    
    // Version 2.5 Changes...(Commented below lines)
    //    myString = [myString stringByReplacingOccurrencesOfString:@"bitefx:DELE_" withString:@""];
    //    myString = [myString stringByReplacingOccurrencesOfString:@"bitefx:HERE_" withString:@""];
    //    myString = [myString stringByReplacingOccurrencesOfString:@"bitefx:SKIP_" withString:@""];
    //    myString = [myString stringByReplacingOccurrencesOfString:@"bitefx:PLUS_" withString:@""];
    
    NSLog(@"New Data to String ==== %@",myString);
    
    NSData * xmlData = [myString dataUsingEncoding:NSUTF8StringEncoding];
    
    NSXMLParser *xmlParser = [[NSXMLParser alloc] initWithData:xmlData];
    
    // Version 2.5 Changes...(Commented below lines)
    //    XMLParser *parser = [[XMLParser alloc] initXMLParser];
    //
    //    //Set delegate
    //    [xmlParser setDelegate:parser];
    
    XMLParserUpdate *parserUpdate = [[XMLParserUpdate alloc] initXMLParserUpdate];
    
    //Set delegate
    xmlParser.delegate = parserUpdate;
    
    // Version 2.5 change support for app upgrade with updates list...
    // Handle <UPGRADE/> tag in XML parsing...
    appDelegate.isUpgradeAvailable = NO;
    
    //Start parsing the XML file.
    BOOL successParse = [xmlParser parse];
    if(successParse){
        
        //if having anyProblem With Version Update remove this code
        if(intVersion == 9)
        {
            strSql = [NSString stringWithFormat:@"update UpdatesList set UpdateDownloaded = 1 where UpdateVersion = %ld",(long)intVersion];
            
            [[Database shareDatabase] Update:strSql];
        }
        
        // Version 2.5 change support for app upgrade with updates list...
        // This tag in MVI file represents that new app version is available. Show message to user...
        if (appDelegate.isUpgradeAvailable) {
            
            // Show message to user that app upgrade is available...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:APP_NEW_VERSION_AVAILABLE cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            return;
        }
        
        NSLog(@"update version set for no %ld No Errors",(long)intVersion);
        strSql = [NSString stringWithFormat:@"update UpdatesList set UpdateDownloaded = 1 where UpdateVersion = %ld",(long)intVersion];
        [[Database shareDatabase] Update:strSql];
    }
    else {
        
        NSLog(@"startXMLParsing Error Error Error!!!");
    }
    
}

- (NSString *)getFileNameFromURL:(NSString *)strURL {
    
    NSArray *arr = [strURL componentsSeparatedByString:@"/"];
    return arr.lastObject;
}

#pragma mark - Check Update clicked

- (IBAction)onClickCheckForUpdate:(id)sender {
    
    [self NoInternetConnectionShowAlert];
    
    [UserDefaults  setObject:[NSDate date] forKey:@"lastCheckForUpdate"];
    
    if (userSubStat == Subscribed) {
        
        [self callUpdateCheckWebservice:nil];
        
    } else if (userSubStat == InAppSubscribed) {
        
        [self callUpdateCheckForInAppWebservice:nil];
    }
    
    lblSubtitle.text = [NSString stringWithFormat:@"Last check for updates: %@",[dateFormatter stringFromDate:[NSDate date]]];
}

- (void)callUpdateCheckWebservice:(id)sender {
    
    isUpdatesChecked = TRUE;
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    body[@"method"] = UPDATESLIST;
    
    if(![UserDefaults boolForKey:@"UpdateDownloaded"]) {
        body[@"update_version"] = @"0";
    } else {
        int Versionnum = [self getVersionToCheckUpdate];
        if(Versionnum>0) {
            NSString *aStrVersion = [NSString stringWithFormat:@"%d",Versionnum];
            body[@"update_version"] = aStrVersion;
        }
    }
    
    NSString *aStrNameQuery = [NSString stringWithFormat:selectAllDataFromUserTable];
    NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
    body[@"emailID"] = aMutArrNames[0][@"emailId"];
    body[@"serialNumber"] = aMutArrNames[0][@"SerialNumber"];
    //    CallWebService *webservice;
    //    webservice=[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"Check Update"];
    
    WebService *aWebService = [[WebService alloc] init];
    [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
        
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            return;
        }
        NSDictionary *aDictResponse = object;
        [self checkUpdate:aDictResponse];
    }];
    
}
- (void)callUpdateCheckForInAppWebservice:(id)sender {
    
    if(![UserDefaults boolForKey:@"UpdateDownloaded"]) {
        
        isUpdatesChecked = TRUE;
        
        NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
        
        //    NSMutableArray *LoginArr = [[NSMutableArray alloc]init];
        body[@"method"] = VERIFY_FOR_UPDATESLIST;
        
        body[@"update_version"] = @"0";
        
        NSMutableArray *aMutArr = [[NSMutableArray alloc] init];
        
        //        [aMutArr addObject:@"1234"];
        
        NSString *strReceipt = @"";
        
        NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
        
        NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
        
        if (aMutArrNames.count > 0) {
            
            strReceipt = aMutArrNames[0][@"Receipt"];
            
        } else {
            
            if ([UserDefaults  objectForKey:@"Receipt"]) {
                
                strReceipt = [UserDefaults  objectForKey:@"Receipt"];
            }
            
            if ([UserDefaults  objectForKey:@"InsertQuery"]) {
                
                [SharedDatabase Insert:[UserDefaults  objectForKey:@"InsertQuery"]];
            }
        }
        //        }
        
        strReceipt = [Utility getInAppPurchaseReceipt];
        [aMutArr addObject:strReceipt];
        
        body[@"udid"] = UDIDForWS;
        
        body[@"receipts"] = aMutArr;
        
        //        CallWebService *webservice;
        //        webservice=[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"Check Update InApp"];
        
        WebService *aWebService = [[WebService alloc] init];
        [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
            
            if (aError) {
                // Show error message...
                [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                return;
            }
            NSDictionary *aDictResponse = object;
            [self checkUpdateInApp:aDictResponse];
        }];
        
    }
    else {
        
        isUpdatesChecked = TRUE;
        
        NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
        
        body[@"method"] = VERIFY_FOR_UPDATESLIST;
        int Versionnum = [self getVersionToCheckUpdate];
        
        
        if(Versionnum>0)
        {
            NSString *aStrVersion = [NSString stringWithFormat:@"%d",Versionnum];
            body[@"update_version"] = aStrVersion;
        }
        NSMutableArray *aMutArr = [[NSMutableArray alloc] init];
        
        NSString *strReceipt = @"";
        
        if ([skPaymentTrasCurrent isKindOfClass:[SKPaymentTransaction class]])
        {
            if  (skPaymentTrasCurrent)
            {
                strReceipt = [Utility getInAppPurchaseReceipt];
            }
        }
        else
        {
            NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
            
            NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
            
            if (aMutArrNames.count > 0)
            {
                strReceipt = aMutArrNames[0][@"Receipt"];
            }
            else
            {
                if ([UserDefaults  objectForKey:@"Receipt"])
                {
                    strReceipt = [UserDefaults  objectForKey:@"Receipt"];
                }
                
                if ([UserDefaults  objectForKey:@"InsertQuery"])
                {
                    [SharedDatabase Insert:[UserDefaults  objectForKey:@"InsertQuery"]];
                }
            }
        }
        
        strReceipt = [Utility getInAppPurchaseReceipt];
        [aMutArr addObject:strReceipt];
        
        body[@"receipts"] = aMutArr;
        
        body[@"udid"] = UDIDForWS;
        //        CallWebService *webservice;
        //        webservice=[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"Check Update"];
        
        WebService *aWebService = [[WebService alloc] init];
        [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
            
            if (aError) {
                // Show error message...
                [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                return;
            }
            NSDictionary *aDictResponse = object;
            [self checkUpdate:aDictResponse];
        }];
        
    }
}

/* Not in use...
 - (void)callingCheckingService {
 
 NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
 
 [body setObject:@"updatesList3"forKey:@"method"];
 
 [body setObject:@"0" forKey:@"update_version"];
 
 NSString *aStrNameQuery = [NSString stringWithFormat:selectAllDataFromUserTable];
 NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
 [body setObject:[[aMutArrNames objectAtIndex:0] objectForKey:@"emailId"] forKey:@"emailID"];
 [body setObject:[[aMutArrNames objectAtIndex:0] objectForKey:@"SerialNumber"] forKey:@"serialNumber"];
 CallWebService *webservice;
 webservice=[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"Check Update"];
 }
 */

#pragma mark - Download Updates

- (IBAction)btnDownloadUpdate:(id)sender {
    intUpdateCounter=0;
    appDelegate.isDownloadManually = NO;
    
    [UserDefaults  setBool:YES forKey:@"isUpdateBtnClicked"];
    
    if(AppDelegateobj.connected) {
        NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
        arrFileDownloadList = [[Database shareDatabase] getFirstColumnForQuery:sql];
        
        if (arrFileDownloadList.count > 0) {
            
            AppDelegateobj.isFullversionDownloading = TRUE;
            AppDelegateobj.isUpdateDownloading = FALSE;
            
            // tag REMAIN_DOWNLOAD
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"Please download full version first. Tap 'Yes' to start download immediately." cancelButtonTitle:@"NO" destructiveButtonTitle:nil otherButtonTitles:@[@"YES"] tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                [self dismissViewControllerAnimated:YES completion:nil];
                if(buttonIndex == controller.cancelButtonIndex)
                {
                    self->isUpdateInterrupted = 1;
                    [self->viewDownloadingData removeFromSuperview];
                }
                else
                {
                    [self btnRemainDownLoadYesClicked];
                }
            }];
            return;
        }
        
        NSString *path=[DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"Update0001"];
        [[NSFileManager defaultManager]createDirectoryAtPath:path withIntermediateDirectories:NO attributes:Nil error:Nil];
        
        //        NSString *strSql = [NSString stringWithFormat:@"select * from UpdatesList where UpdateDownloaded = 0"];
        NSString *strSql = [NSString stringWithFormat:@"select * from FileUpdateList where IsDownloaded = 0"];
        
        intVideoCounter = 0;
        arrFileDownloadList = [[Database shareDatabase] getAllDataForQuery:strSql];
        if (arrFileDownloadList.count > 0) {
            
            [self startUpdateDownloading];
            
        } else {
            // tag : 0
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"All files downloaded successfully." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            
            // Version 2.5 change support for app upgrade with updates list...
            // When new app upgrade is available and user click on "Download Update" show message to user that new vesrion is available in App Store.
            // For that we need to parse MVI files and check <UPGRADE/> is available or not...
            [self startXMLParsingForUpdatedMVI];
        }
        
    } else {
        // tag : 0
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:definenoInternetConnection cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        return;
    }
}

#pragma mark - FullVersionDownloading

- (void)FullVersionDownloading:(NSString *)aStr {
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    
    body[@"method"] = @"downloadUpdate2";
    if(FullorUpdate==1) {
        
        body[@"version"] = @"0";
        
    } else {
        body[@"version"] = @"1";
        
    }
    body[@"file"] = aStr;
    
    CallWebService *webservice=[[CallWebService alloc] initWithURL:DownloadURL delegate:self args:body key:@"DownloadUpdate"];
    webservice.progressDelegate  = self;
    
}


- (void)updateTotalProgress:(float)aFloatProgVal {
    
}

- (void)updateProgress:(float)aFloatProgVal {
    
    int  value = (int)(aFloatProgVal*100);
    
    NSString *aStrprogresstext = [NSString stringWithFormat:@"%d",value];
    lblProgresspercent.text = [aStrprogresstext stringByAppendingString:@"%Completed"];
    progressBar.hidden = NO;
    progressBar.progressViewStyle = UIProgressViewStyleDefault;
    progressBar.progress = aFloatProgVal/1.0;
}

/*
 - (void)processFinished:(NSString*)aStrMessage {
 
 lblProgresspercent.hidden = FALSE;
 progressBar.hidden = FALSE;
 
 if(FullorUpdate==1) {
 isFullVersionDownloaded = TRUE;
 isUpdateDownloaded = FALSE;
 [UserDefaults setBool:YES forKey:@"FullDownloaded"];
 [UserDefaults synchronize];
 [UserDefaults setBool:NO forKey:@"UpdateDownloaded"];
 [UserDefaults synchronize];
 
 } else {
 
 isFullVersionDownloaded = FALSE;
 [UserDefaults setBool:NO forKey:@"FullDownloaded"];
 [UserDefaults synchronize];
 isUpdateDownloaded = TRUE;
 [UserDefaults setBool:YES forKey:@"UpdateDownloaded"];
 [UserDefaults synchronize];
 }
 
 [UserDefaults setBool:YES forKey:@"Registered"];
 [UserDefaults synchronize];
 
 [self ShowAlertMessageWithOkAndCancelTitle:ErrorTitle alertMessage:aStrMessage cancelButtonTitle:strAlertCancelTitle alertTag:999];
 }
 */

#pragma  mark -
#pragma CancelDownloadUpdateonClick

- (IBAction)CancelDownloadUpdate:(id)sender {
    
    if(viewUpdates)
        [viewUpdates removeFromSuperview];
    //To check whether update is downloaded and then unzip the downloaded file
}

#pragma mark--
#pragma mark ResumingDownload

- (void)resumeDownload {
    
    AppDelegateobj.isDownloadCancelled = FALSE;
    [self downLoadFile];
}

#pragma mark--
#pragma mark Next-Previous Button
- (IBAction) clickPreviousFrame {
    
    if (appDelegate.mutArrPlayVideo.count == 0) {
        return;
    }
    
    if(arrPlayerItems.count>0)
    {
        if(currentIndex < arrPlayerItems.count && currentIndex >= 1)
        {
            currentIndex--;
            [self mangePhotoVideoFlow:currentIndex];
        }
    }
}
- (IBAction) clickPreviousFrame1 {
    
    if(currentIndex ==0)
    {
        [self SetPlayerToNormalStateWithPause];
        return;
    }
    [self SetTimerframeConterInvalidate];
    
    if((AppDelegateobj.mutArrPlayVideo).count>1)
    {
        lblFramcounter.text = @"1";
    }
    else
    {
        [self SetPlayerToNormalStateWithPause];
    }
    
    if((AppDelegateobj.mutArrPlayVideo).count>0) {
        
        [btnNextFrame setEnabled:YES];
        sliderTimeForVideo.value = 0.0;
        isPreviousClicked = YES;
        isPlaying=FALSE;
        [self.m_queueplayer pause];
        [self setPlayPauseImageForNormalState];
        [btnPreviousFrame setImage:[UIImage imageNamed:@"PreviousButton.png"] forState:UIControlStateNormal];
        
        if (currentIndex > 0)
        {
            currentIndex--;
            [btnNextFrame setEnabled:YES];
            [btnPreviousFrame setEnabled:NO];
            
            if([UserDefaults boolForKey:@"UpdateDownloaded"])
            {
                if((AppDelegateobj.mutArrPlayVideo).count>0) {
                    
                    if([(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"thumbnailPath"]rangeOfString:@"/Documents/Update0001"].length > 0)
                    {
                        AppDelegateobj.isUpdateDownloading = TRUE;
                        (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                        
                        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                        [SharedDatabase Update:aStrUpdateSql];
                    }
                }
                
            }  else {
                
                if((AppDelegateobj.mutArrPlayVideo).count>0) {
                    
                    (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                    
                    NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                    [SharedDatabase Update:aStrUpdateSql];
                }
            }
            
            [self playVideoAtIndex:currentIndex];
            
            AppDelegateobj.intSelectedVideo = currentIndex;
            
            [self LoadWbViewWithURL];
            
            [self showHideFileTitleWithAnimation];
        }
    }
}
-(IBAction)clickNextFrame {
    
    if (appDelegate.mutArrPlayVideo.count == 0) {
        return;
    }
    
    if(arrPlayerItems.count>0)
    {
        if(currentIndex < arrPlayerItems.count-1)
        {
            currentIndex++;
            [self mangePhotoVideoFlow:currentIndex];
        }
    }
}
-(IBAction)clickNextFrame1 {
    
    [self SetTimerframeConterInvalidate];
    
    if(currentIndex+1== (appDelegate.mutArrPlayVideo).count)
    {
        [self SetPlayerToNormalStateWithPause];
        return;
    }
    if((appDelegate.mutArrPlayVideo).count>0)
    {
        lblFramcounter.text = @"1";
    }
    else
    {
        [self SetPlayerToNormalStateWithPause];
    }
    
    if((appDelegate.mutArrPlayVideo).count>0)
    {
        [self.m_queueplayer pause];
        isPlaying=FALSE;
        [self setPlayPauseImageForNormalState];
        isnextClicked=TRUE;
        if (currentIndex < arrPlayerItems.count-1) {
            currentIndex++;
            
            //Rohan
            if(currentIndex+1== (AppDelegateobj.mutArrPlayVideo).count)
            {
                [btnNextFrame setEnabled:NO];
                [btnPreviousFrame setEnabled:YES];
            }else{
                [btnNextFrame setEnabled:YES];
                [btnPreviousFrame setEnabled:YES];
            }
            
            if([UserDefaults boolForKey:@"UpdateDownloaded"])
            {
                
                if((AppDelegateobj.mutArrPlayVideo).count>0)
                {
                    if([(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"thumbnailPath"]rangeOfString:@"/Documents/Update0001"].length > 0)
                    {
                        AppDelegateobj.isUpdateDownloading = TRUE;
                        (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                        
                        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                        [SharedDatabase Update:aStrUpdateSql];
                    }
                }
                
            }  else {
                
                if((AppDelegateobj.mutArrPlayVideo).count>0) {
                    
                    (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                    
                    NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                    [SharedDatabase Update:aStrUpdateSql];
                }
            }
            
            [self playVideoAtIndex:currentIndex];
            
            
            AppDelegateobj.intSelectedVideo = currentIndex;
            
            [self LoadWbViewWithURL];
            [self showHideFileTitleWithAnimation];
        }
        
    }
}

#pragma mark - Other Methods
#pragma mark Gesture methods
-(void)leftswipe:(UISwipeGestureRecognizer*)sender
{
    [self clickNextFrame];
}

-(void)leftswipe1:(UISwipeGestureRecognizer*)sender
{
    [self SetTimerframeConterInvalidate];
    if(currentIndex+1== (AppDelegateobj.mutArrPlayVideo).count)
    {
        [self SetPlayerToNormalStateWithPause];
        return;
    }
    
    if((AppDelegateobj.mutArrPlayVideo).count>1)
    {
        lblFramcounter.text = @"1";
    }
    else
    {
        [self SetPlayerToNormalStateWithPause];
    }
    
    if((AppDelegateobj.mutArrPlayVideo).count>0)
    {
        [m_playerView addSubview:m_indicaterView];
        [lbl_view setHidden:NO];
        m_indicaterView.alpha = 0.5;
        lbl_view.alpha = 1.0;
        [m_indicaterView setHidden:FALSE];
        if(btnInfo.selected)
        {
            lbl_view.frame = CGRectMake(200,300, 200, 50);
            lbl_view.text = @"Next Video";
            [lbl_view setTextColor:defineBlackColor];
            lbl_view.backgroundColor = [UIColor clearColor];
            lbl_view.font = [UIFont boldSystemFontOfSize:35];
            lbl_view.textAlignment = NSTextAlignmentCenter;
        }
        else
        {
            lbl_view.frame = CGRectMake(327, 374, 500, 50);
            lbl_view.text = @"Next Video";
            [lbl_view setTextColor:defineBlackColor];
            lbl_view.backgroundColor = [UIColor clearColor];
            lbl_view.font = [UIFont boldSystemFontOfSize:60];
            lbl_view.textAlignment = NSTextAlignmentLeft;
            [lbl_view setHighlightedTextColor:defineBlackColor];
            
        }
        [UIView beginAnimations:nil context:NULL];
        [UIView setAnimationDuration:0.5];
        
        [UIView setAnimationDidStopSelector:@selector(hidelbl)];
        [UIView setAnimationDelegate:self];
        lbl_view.alpha=0.0;
        m_indicaterView.alpha = 1.0;
        m_indicaterView.backgroundColor=defineBlackColor;
        
        [UIView commitAnimations];
        
        m_indicaterView.alpha = 0.0;
        
        [self.m_queueplayer pause];
        
        isPlaying=FALSE;
        [self setPlayPauseImageForNormalState];
        
        isnextClicked=YES;
        
        if (currentIndex < arrPlayerItems.count-1) {
            currentIndex++;
            
            if([UserDefaults boolForKey:@"UpdateDownloaded"]) {
                
                intUpdatesdownloadCompleted = 1;
                if((AppDelegateobj.mutArrPlayVideo).count>0)
                {
                    if([(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"thumbnailPath"]rangeOfString:@"/Documents/Update0001"].length > 0)
                    {
                        AppDelegateobj.isUpdateDownloading = TRUE;
                        (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                        
                        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                        [SharedDatabase Update:aStrUpdateSql];
                    }
                }
                
            } else {
                
                if((AppDelegateobj.mutArrPlayVideo).count>0) {
                    
                    (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                    
                    NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                    [SharedDatabase Update:aStrUpdateSql];
                }
            }
            
            [self playVideoAtIndex:currentIndex];
            
            AppDelegateobj.intSelectedVideo = currentIndex;
            
            [self LoadWbViewWithURL];
            [self showHideFileTitleWithAnimation];
        }
        
    }
}

- (void)Rightswipe:(UISwipeGestureRecognizer*)sender {
    [self clickPreviousFrame];
}

- (void)Rightswipe1:(UISwipeGestureRecognizer*)sender {
    
    [self SetTimerframeConterInvalidate];
    
    if(currentIndex ==0)
    {
        [self SetPlayerToNormalStateWithPause];
        return;
    }
    
    if((AppDelegateobj.mutArrPlayVideo).count>1)
    {
        lblFramcounter.text = @"1";
    }
    else
    {
        [self SetPlayerToNormalStateWithPause];
    }
    if((AppDelegateobj.mutArrPlayVideo).count>0)
    {
        
        intCount++;
        [lbl_view setHidden:NO];
        [m_playerView addSubview:m_indicaterView];
        
        m_indicaterView.alpha =.5;
        lbl_view.alpha=1.0;
        
        [m_indicaterView setHidden:FALSE];
        
        if(btnInfo.selected)
        {
            lbl_view.frame = CGRectMake(200, 300, 300, 50);
            lbl_view.text = @"Previous Video";
            [lbl_view setTextColor:defineBlackColor];
            lbl_view.backgroundColor = [UIColor clearColor];
            lbl_view.font = [UIFont boldSystemFontOfSize:35];
            lbl_view.textAlignment = NSTextAlignmentLeft;
        }
        else
        {
            lbl_view.frame = CGRectMake(327, 374, 500, 50);
            lbl_view.text = @"Previous Video";
            
            [lbl_view setTextColor:defineBlackColor];
            lbl_view.backgroundColor = [UIColor clearColor];
            lbl_view.font = [UIFont boldSystemFontOfSize:60];
            lbl_view.textAlignment = NSTextAlignmentLeft;
            [lbl_view setHighlightedTextColor:defineBlackColor];
            
        }
        
        [UIView beginAnimations:nil context:NULL];
        [UIView setAnimationDuration:0.5];
        [UIView setAnimationDidStopSelector:@selector(hidelbl)];
        [UIView setAnimationDelegate:self];
        lbl_view.alpha=.0;
        
        m_indicaterView.alpha = 1.0;
        m_indicaterView.backgroundColor=defineBlackColor;
        
        [UIView commitAnimations];
        
        m_indicaterView.alpha = 0.0;
        
        isPreviousClicked=YES;
        if (currentIndex > 0)
        {
            currentIndex--;
            
            if([UserDefaults boolForKey:@"UpdateDownloaded"])
            {
                if((AppDelegateobj.mutArrPlayVideo).count>0)
                {
                    if([(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"thumbnailPath"]rangeOfString:@"/Documents/Update0001"].length > 0)
                    {
                        AppDelegateobj.isUpdateDownloading = TRUE;
                        (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                        
                        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                        [SharedDatabase Update:aStrUpdateSql];
                    }
                }
                
            } else {
                
                if((AppDelegateobj.mutArrPlayVideo).count>0) {
                    
                    (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
                    
                    NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
                    [SharedDatabase Update:aStrUpdateSql];
                }
            }
            
            [self playVideoAtIndex:currentIndex];
            
            AppDelegateobj.intSelectedVideo = currentIndex;
            
            [self LoadWbViewWithURL];
            [self showHideFileTitleWithAnimation];
        }
        isPlaying=FALSE;
        [self setPlayPauseImageForNormalState];
        
    }
}
- (IBAction)btnHelpClicked:(UIButton*)sender {
    if (btnAnimationPanel.selected) {
        btnHelp.selected = NO;
        btnHelp.enabled=YES;
        btnInfo.enabled=NO;
        btnAnimationPanel.enabled=YES;
    } else {
        if(sender.tag==0) {
            btnHelp.selected = NO;
            btnHelp.enabled=YES;
            btnMenu.enabled=YES;
            btnAnimationPanel.enabled=YES;
            //            btnNextFrame.enabled=YES;
            //            btnPreviousFrame.enabled=YES;
            
            //===================
            // Version 3.1 Changes
            //===================
            // Solve Prev - Next button is enable/disabled issue from client comment.
            //Rohan Condition
            NSInteger total = (AppDelegateobj.mutArrPlayVideo).count;
            NSInteger aintSelectedVideoIndex = AppDelegateobj.intSelectedVideo;
            
            if (total == 1) {
                [btnNextFrame setEnabled:NO];
                [btnPreviousFrame setEnabled:NO];
            }else if (aintSelectedVideoIndex == total-1) {
                [btnNextFrame setEnabled:NO];
                [btnPreviousFrame setEnabled:YES];
            }else{
                if (aintSelectedVideoIndex == 0) {
                    [btnNextFrame setEnabled:YES];
                    [btnPreviousFrame setEnabled:NO];
                }else{
                    [btnNextFrame setEnabled:YES];
                    [btnPreviousFrame setEnabled:YES];
                }
            }
            
            if (!isImageDisplay) {
                
                btnInfo.enabled=YES;
                self.sliderSpeedControl.enabled=YES;
                sliderTimeForVideo.enabled=YES;
                playPauseBtn.enabled=YES;
                loopBtn.enabled=YES;
                btn_SpeedControl.enabled = YES;
            }
            btn_SpeedControl.alpha =1.0f;
        }
    }
    [btnHelpView removeFromSuperview];
    [btnHelpClose removeFromSuperview];
}

- (IBAction)valueChanged:(id)sender {
    
    floatSpeedMultiplier = sliderSpeedControl.value;
    // self.sliderSpeedControl.value = floatSpeedMultiplier;
    
    NSString *sldVal = [NSString stringWithFormat:@"%.1f", sliderSpeedControl.value];
    floatSpeedMultiplier = sldVal.floatValue;
    Speed = floatSpeedMultiplier;
    if(isPlaying)
    {
        self.m_queueplayer.rate = Speed;
    }
    
    if(currentIndex < appDelegate.mutArrPlayVideo.count)
    {
        NSMutableDictionary *objDict = (appDelegate.mutArrPlayVideo)[currentIndex];
        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set moviePlaySpeed = '%.1f' where fileID=\'%@\'",Speed,objDict[@"filesID"]];
        [SharedDatabase Update:aStrUpdateSql];
        objDict[@"moviePlaySpeed"] = [NSString stringWithFormat:@"%.1f", Speed];
    }
    
    
}

- (IBAction)LoopBtnAction:(id)sender {
    
    if(!loopBtn.selected) {
        
        loopBtn.selected = YES;
        
    } else {
        
        loopBtn.selected = NO;
    }
}

- (void)hidelbl {
    
    [lbl_view setHidden:YES];
}

- (IBAction)play:(id)sender {
    
    
    
    if (sliderTimeForVideo.maximumValue == sliderTimeForVideo.value) {
        [self.m_queueplayer seekToTime:kCMTimeZero toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero];
    }
    
    float frames = [UserDefaults floatForKey:@"frames"];
    
    intCount = 0;
    
    float time = [UserDefaults floatForKey:@"videoTime"];
    //float val = (time/Speed);
    floatCount = time/frames;
    intCount = 1;
    //    NSLog(@"Frame: %d",intCount);
    
    //Set timer counter
    [self SetTimerframeConterInvalidate];
    timerFrameCounter = [[NSTimer alloc]init];
    timerFrameCounter =  [NSTimer scheduledTimerWithTimeInterval:floatCount target:self selector:@selector(labelValueChange) userInfo:nil repeats:YES];
    
    if(m_indicaterView!=nil) {
        [m_indicaterView removeFromSuperview];
    }
    
    
    
    
    if (isPlaying) {
        
        isanimationFinished = FALSE;
        [self.m_queueplayer pause];
        
        [self setPlayPauseImageForNormalState];
        
        if (appDelegate.mutArrPlayVideo.count > 0) {
            [UIView animateWithDuration:0.50 animations:^{
                [self displayFileTitle];
                [self HideFileTitle];
            } completion:^(BOOL finished) {
            }];
        }
    } else {
        NSMutableDictionary *objDict = [NSMutableDictionary new];
        if (appDelegate.mutArrPlayVideo.count > 0) {
            
            objDict = (appDelegate.mutArrPlayVideo)[currentIndex];
            
            NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where fileID=\'%@\'",objDict[@"filesID"]];
            
            [SharedDatabase Update:aStrUpdateSql];
            
            [self HideFileTitle];
        }
        [playPauseBtn setImage:[UIImage imageNamed:@"pauseNormal.png"] forState:UIControlStateNormal];
        if(appDelegate.mutArrPlayVideo.count > currentIndex)
        {
            NSMutableDictionary *objDict = (appDelegate.mutArrPlayVideo)[currentIndex];
            
            NSString *spdVal = objDict[@"moviePlaySpeed"];
            
            if([spdVal isEqualToString:@""] || [spdVal isEqualToString:@"0.0"] || [spdVal.lowercaseString containsString:@"null"] || [spdVal.lowercaseString containsString:@"nil"] || spdVal == nil)
            {
                spdVal = @"1.0";
            }
            Speed = spdVal.floatValue;
        }
        else
        {
            Speed = 1.0;
        }
        self.sliderSpeedControl.value = Speed;
        self.m_queueplayer.rate = Speed;
        
        if(isanimationFinished) {
            [self.m_queueplayer seekToTime:kCMTimeZero toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero];
        }
        if (sliderTimeForVideo.maximumValue == currentFrame) {
            [self.m_queueplayer seekToTime:kCMTimeZero toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero];
        }
    }
    isPlaying = !isPlaying;
    
    [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(showTime) userInfo:nil repeats:NO];
    self.m_queueplayer.actionAtItemEnd = AVPlayerActionAtItemEndNone;
}

- (void) showTime {
    
    CMTime time = [self.m_queueplayer currentTime];
    
    if (CMTIME_IS_VALID(time) && isPlaying) {
        [self showTimeOnScroll:CMTimeGetSeconds(time)];
        
        [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(showTime) userInfo:nil repeats:NO];
        
    } else {
        
        // [self.m_queueplayer seekToTime:kCMTimeZero toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero];
        
        NSLog(@"time is invalid");
    }
}

- (IBAction)TouchupInsideSlider {
}

- (IBAction)valueChangeSliderTime {
    
    // intCount = 0;
    
    [self SetTimerframeConterInvalidate];
    
    float frames = [UserDefaults floatForKey:@"frames"];
    currentFrame = ((frames*sliderTimeForVideo.value)/sliderTimeForVideo.maximumValue);
    if(currentFrame<1)
    {
        currentFrame = 1;
    }
    
    
    if (sliderTimeForVideo.maximumValue == sliderTimeForVideo.value) {
        NSLog(@"at the end  ");
    }
    
    lblFramcounter.text = [NSString stringWithFormat:@"%d",currentFrame];
    //timerFrameCounter =  [NSTimer scheduledTimerWithTimeInterval:floatCount target:self selector:@selector(labelValueChangeaccordingSlider) userInfo:nil repeats:NO];
    isanimationFinished = FALSE;
    [self SetPlayerToNormalStateWithPause];
    
    float timeInSecond = sliderTimeForVideo.value;
    //    timeInSecond *= 1000;
    
    //    CMTime cmTime = CMTimeMake(timeInSecond, 1000);
    isSlidertouchedDuringPlaying = TRUE;
    
    CMTime cmTime = CMTimeMakeWithSeconds(timeInSecond, self.mediaTimeScale);
    
    [self.m_queueplayer seekToTime:cmTime toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero];
    
}
#pragma mark - Button skip clicked
- (IBAction)btnUninstallUpdatesClicked:(id)sender{
    // tag 99
    
    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"Are you sure you want to Uninstall updates ?" cancelButtonTitle:@"YES" destructiveButtonTitle:nil otherButtonTitles:@[@"NO"] tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
        [self dismissViewControllerAnimated:YES completion:nil];
        if (buttonIndex == controller.cancelButtonIndex)
        {
            // YES they want to uninstall updates.
            NSFileManager *fm = [NSFileManager defaultManager];
            
            NSString *directoryUpdate = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"Update0001/"];
            NSError *error = nil;
            
            for (NSString *file in [fm contentsOfDirectoryAtPath:directoryUpdate error:&error]) {
                BOOL success1 = [fm removeItemAtPath:[NSString stringWithFormat:@"%@/%@", directoryUpdate, file] error:&error];
                if (!success1 || error) {
                    NSLog(@"%@",error);
                }
            }
            
            
            NSString *strSql = [NSString stringWithFormat:@"select * from LocalFileMaster where isDownloaded = 0"];
            NSMutableArray *aTempArray = [NSMutableArray new];
            [aTempArray addObjectsFromArray:[[Database shareDatabase] getAllDataForQuery:strSql]];
            
            for (NSDictionary *aDic in aTempArray) {
                
                NSString *aStrFilePath = [NSString stringWithFormat:@"%@",aDic[@"filePath"]];
                NSString *aStrInfo = [NSString stringWithFormat:@"%@",aDic[@"infoFilePath"]];
                
                NSString *aWord = @"Update0001/";
                if ([aStrFilePath rangeOfString:aWord].location != NSNotFound) {
                    
                    NSString *aStrNewPath = @"";
                    
#pragma mark --------------------------------
#pragma mark Version 2.4 Folder Structure Changes
                    if([aDic[@"fileType"] isEqualToString:mediaMovie]) {
                        
                        aStrNewPath = [NSString stringWithFormat:@"%@/%@/", BITEFXV2, MOVIES];
                        aStrFilePath = [aStrFilePath stringByReplacingOccurrencesOfString:aWord withString:aStrNewPath];
                        aStrInfo = [aStrInfo stringByReplacingOccurrencesOfString:aWord withString:aStrNewPath];
                    }
                    else
                    {
                        aStrNewPath = [NSString stringWithFormat:@"%@/%@/", BITEFXV2, IMAGES];
                        aStrFilePath = [aStrFilePath stringByReplacingOccurrencesOfString:aWord withString:aStrNewPath];
                        aStrInfo = @"";
                    }
                    
                    //                    aStr = [aStr stringByReplacingOccurrencesOfString:aWord withString:@"BiteFXiPadFull/"];
                    //                    aStrInfo = [aStrInfo stringByReplacingOccurrencesOfString:aWord withString:@"BiteFXiPadFull/"];
                    
                    NSString *directoryUpdateTemp = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:aStrFilePath];
                    BOOL aSuccess = [fm fileExistsAtPath:directoryUpdateTemp];
                    if (aSuccess) {
                        NSLog(@"Record Found");
                        NSString *aTempUpdate = [NSString stringWithFormat:@"update LocalFileMaster set filePath== \'%@\' where fileID== \'%@\'",aStrFilePath,aDic[@"fileID"]];
                        [SharedDatabase Update:aTempUpdate];
                        
                        NSString *aTempInfoFile = [NSString stringWithFormat:@"update LocalFileMaster set infoFilePath== \'%@\' where fileID== \'%@\'",aStrInfo,aDic[@"fileID"]];
                        [SharedDatabase Update:aTempInfoFile];
                        
                        NSString *aStrUpdatePlayRecord = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where fileID=\'%@\'",aDic[@"fileID"]];
                        [SharedDatabase Update:aStrUpdatePlayRecord];
                        
                    }else{
                        NSString *aTempDeleted = [NSString stringWithFormat:@"DELETE FROM LocalFileMaster where fileID= \'%@\'",[NSString stringWithFormat:@"%@",aDic[@"fileID"]]];
                        [SharedDatabase Delete:aTempDeleted];
                        
                        [self deleteUserDefineSequenceForUninstallUpdate:[NSString stringWithFormat:@"%@",aDic[@"fileID"]]];
                        
                    }
                }
            }
            
            NSString *aFileUpdateList = @"update FileUpdateList set IsDownloaded=0";
            [SharedDatabase Update:aFileUpdateList];
            
            NSString *aUpdatesList = @"update UpdatesList set UpdateDownloaded=0";
            [SharedDatabase Update:aUpdatesList];
            
            [UserDefaults  setBool:YES forKey:@"FullDownloaded"];
            
#pragma mark --------------------------------
#pragma mark Version 2.4 Changes
            // Don't delete UserDefined photos...
            
            NSString *aStrQuery = [NSString stringWithFormat:@"select collectionID from CollectionMaster where isUserDefine = \'1\'"];
            NSMutableArray *aMutArrCollectionIds = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
            
            NSString *aStrDeleteQuery = @"DELETE FROM CollectionFilesMaster";
            
            NSString *aStrCollectionIds = @"";
            
            for (int i = 0; i < aMutArrCollectionIds.count; i++) {
                if (i == 0)
                {
                    aStrCollectionIds = [aMutArrCollectionIds[i] valueForKey:@"collectionID"];
                }
                else
                {
                    aStrCollectionIds = [NSString stringWithFormat:@"%@,%@", aStrCollectionIds, [aMutArrCollectionIds[i] valueForKey:@"collectionID"]];
                }
                
            }
            if(aMutArrCollectionIds.count > 0) {
                
                aStrDeleteQuery = [NSString stringWithFormat:@"DELETE FROM CollectionFilesMaster where collectionID NOT IN (%@)", aStrCollectionIds];
            }
            
            [SharedDatabase Delete:aStrDeleteQuery];
            [SharedDatabase Delete:@"DELETE FROM CollectionMaster where isUserDefine = \'0\'"];
            
            [self startXMLParsing];
            
            //===================
            // Version 3.1 Changes
            //===================
            // Feature MVI file changes.
            //-----------------
            // Parse Features.MVI again to update FeatureID after deleting data...
            [self startXMLParsingFeatureSet];
            //===================
            
            [self insertPlayListCollection];
            
            [self callUpdateCheckWebservice:nil];
            
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"Data uninstalled successfully" cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            
            [self setMenuButtonNormal];
            [self removeviewPopupFromSuperView];
            
            // Updates are uninstalled.
            // Version 3.0 changes
            // To refresh UI in AnimationPanel after uninstalling update files.
            // This is needed because we enable Menu button as per client requirement.
            // Post Notification that all files are downloaded.
            [[NSNotificationCenter defaultCenter] postNotificationName:REFRESH_ANIMATIOM_PANEL_DATA object:nil];
            
        }
    }];
}


- (IBAction)btnSkipClicked:(id)sender
{
    NSString *aTempStr;
    if(appDelegate.isFullversionDownloading)
    {
        //insert into FileDownloadList ('FileURL','IsDownloaded','isSkipFile') values ('%@',%d,'0')
        ///NSLog(@"is full version");
        aTempStr = @"select * from FileDownloadList where isSkipFile = '1'";
        //NSLog(@"%@",self.mutArrSkipVideoList);
        self.mutArrSkipVideoList = [[Database shareDatabase] getAllDataForQuery:aTempStr];
    }
    else {
        //NSLog(@"is update version");
        //NSLog(@"appDelegate.isUpdateDownloading ==> %hhd",appDelegate.isUpdateDownloading);
        //aTempStr = @"select * from FileUpdateList where isSkipFile = '1'";
        aTempStr = @"select * from FileUpdateList where isSkipFile = '1' and IsDownloaded = 0 order by serial ASC";// group by FileName";
        self.mutArrSkipVideoList = [[Database shareDatabase] getAllDataForQuery:aTempStr];
    }
    
    if(self.mutArrSkipVideoList.count != 0)
    {
        self.mutArrSkipVideoList = [self manageArrayList:self.mutArrSkipVideoList];
    }
    //NSLog(@"%@",self.mutArrSkipVideoList);
    //Code end..
    
    [self setMenuButtonNormal];
    
    [self removeviewPopupFromSuperView];
    if(self.mutArrSkipVideoList.count == 0)
    {
        //[tblViewSkipUpdate setHidden:YES];
        // tag : 0
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"You have no more skip videos." cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        
    }
    else
    {
        [tblViewSkipUpdate setHidden:NO];
        [self.view addSubview:viewSkipList];
        [tblViewSkipUpdate reloadData];
    }
}
- (NSMutableArray *)manageArrayList:(NSMutableArray *)array
{
    
    NSMutableArray *aTempArray = [[NSMutableArray alloc]init];
    NSMutableArray *aTempSkipDownloadArray = [[NSMutableArray alloc]init];
    
    for (int j = 0; j < array.count; j++)
    {
        [aTempArray addObject:array[j]];
        for (int i = 0; i < 3 ; i++)
        {
            [aTempSkipDownloadArray addObject:array[j]];
            if(i != 2)
            {
                j=j+1;
            }
        }
    }
    self.mutArrSkipDownloadList = aTempSkipDownloadArray;
    NSLog(@"%@",aTempArray);
    NSLog(@"%@",self.mutArrSkipDownloadList);
    self.mutArrSkipVideoList = aTempArray;
    return aTempArray;
}
//Code End..
- (IBAction)cancelViewSkipListClicked:(id)sender
{
    if(viewSkipList)
        [viewSkipList removeFromSuperview];
}

-(void)btnSkipDownloadClicked:(UIButton *)sender
{
    intSkipVideoCount = 0;
    isManageArrayList = YES;
    if(viewSkipList)
    {
        [viewSkipList removeFromSuperview];
    }
    appDelegate.isDownloadManually = YES;
    if (AppDelegateobj.isFullversionDownloading)
    {
        appDelegate.isSkipVideoFullVerison = TRUE;
        [self startSkipFullVerisonFileDownload:sender.tag];
    }
    if(appDelegate.isUpdateDownloading)
    {
        appDelegate.isSkipVideoUpdateVerison = TRUE;
        [self startSkipUpdateVersionFileDownload:sender.tag];
    }
}
#pragma mark - start Skip Video Download
- (void)startSkipFullVerisonFileDownload:(NSInteger)index
{
    FullorUpdate = 1;
    UpdateCompleted = FALSE;
    appDelegate.isUpdatedMVIfile = FALSE;
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:YES];
    
    if(intSkipVideoCount == 3)
    {
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        
        lblProgresspercent.text = defineFileDownloadSuccessMessage;
        lblNoofVideoCompleted.text = @"";
        progressBar.hidden = YES;
        
        lblNoofVideoCompleted.text = @"";
        return;
    }
    
    lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of 3 file(s)...",intSkipVideoCount+1];
    
    progressBar.progress = 0;
    if(viewDownloadingData) {
        
        [viewDownloadingData removeFromSuperview];
    }
    if(viewUpdates) {
        
        [viewUpdates removeFromSuperview];
    }
    
    AppDelegateobj.isUpdateDownloading = FALSE;
    AppDelegateobj.isFullversionDownloading = TRUE;
    lblfullorupdate.text = @"Downloading full content...";
    lblThanksRegistration.hidden = FALSE;
    lblThanksRegistration.text = @"Welcome BiteFX Subscriber"; //CR Change @"Thank you for registering with BiteFX";
    
    lblDownloadMsg.text=[NSString stringWithFormat:@" \t\tYour BiteFX content is being downloaded. \n\n\tThis may take a while depending on the speed of your Internet connection.\n\n\tDO NOT exit the BiteFX app during download. \n\nIf the download is interrupted or it is detected that some files did not download correctly, you will have two options to resume downloading the missing items:\n1. Respond 'Yes' to a message BiteFX will display on start-up if it detects the download was interrupted.\n2. Tapping the 'Resume Download' button that is displayed in the BiteFX button bar when there are more files to download."];
    
    if(viewUpdates.superview) {
        
        [viewUpdates removeFromSuperview];
    }
    [self.view addSubview:viewDownloadingData];
    
    if(isManageArrayList)
    {
        [self manageArrayForlisting:index];
    }
    //NSString *aTempStr = [[mutArrSkipVideoList objectAtIndex:index] objectForKey:@"FileURL"];
    NSString *aTempStr = mutArrSkipDownloadList[intSkipVideoCount][@"FileURL"];
    
    //NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,aTempStr];
    
    NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",DEST_PATH,aTempStr];
    
    NSURL *aTempURL = [NSURL URLWithString:aTempStr];
    
    [self.downloadManager download:aTempStr path:strVideoPath withURL:aTempURL];
}

-(void)manageArrayForlisting:(NSInteger)index
{
    isManageArrayList = NO;
    NSMutableArray *aTempMutableArray = [[NSMutableArray alloc]init];
    //[aTempMutableArray addObject:[self.mutArrSkipVideoList objectAtIndex:index]];
    int aTempInt = [(self.mutArrSkipVideoList)[index][@"serial"] intValue];
    for (int i = aTempInt; i < aTempInt+3; i++)
    {
        NSPredicate *predicate = [NSPredicate predicateWithFormat:@"serial = %@",[NSString stringWithFormat:@"%d",i]];
        NSArray *selectedArray = [self.mutArrSkipDownloadList filteredArrayUsingPredicate:predicate];
        if(selectedArray.count > 0)
        {
            //[aTempMutableArray addObject:[self.mutArrSkipDownloadList objectAtIndex:i]];
            [aTempMutableArray addObject:selectedArray[0]];
        }
    }
    [self.mutArrSkipDownloadList removeAllObjects];
    self.mutArrSkipDownloadList = aTempMutableArray;
}
- (void)startSkipUpdateVersionFileDownload:(NSInteger)index
{
    FullorUpdate = 0;
    UpdateCompleted = FALSE;
    
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:YES];
    
    if(intSkipVideoCount == 3)
    {
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        
        
        lblProgresspercent.text = defineFileDownloadSuccessMessage;
        lblNoofVideoCompleted.text = @"";
        progressBar.hidden = YES;
        //Code end...
        //lblProgresspercent.text = defineFileDownloadSuccessMessage;
        
        
        progressBar.hidden = YES;
        UpdateCompleted = TRUE;
        isUpdateInterrupted = 0;
        
        
        NSString *strSql = @"select count(UpdateUniqueId) from UpdatesList where UpdateDownloaded = 0";
        intupdate = [[Database shareDatabase] getCount:strSql];
        
        imgMenuUpdates.hidden = YES;
        lblMenuUpdates.hidden = YES;
        lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
        
        imgUpdatesAvailable.hidden = YES;
        lblUpdatesavailable.hidden  = YES;
        lblUpdatesavailable.text =[NSString stringWithFormat:@"%d",intupdate];
        
        
        //Code Start..
        lblNoofVideoCompleted.text = @"";
        //Code End..
        return;
    }
    if(viewDownloadingData) {
        
        [viewDownloadingData removeFromSuperview];
    }
    
    AppDelegateobj.isUpdateDownloading = TRUE;
    AppDelegateobj.isFullversionDownloading = FALSE;
    
    lblThanksRegistration.hidden = NO;
    lblThanksRegistration.text = @"Welcome BiteFX Subscriber";
    lblfullorupdate.text = defineDownloadingUpdates;
    lblDownloadMsg.text=[NSString stringWithFormat:@" \t\tYour BiteFX content is being downloaded. \n\n\tThis may take a while depending on the speed of your Internet connection.\n\n\tDO NOT exit the BiteFX app during download. \n\nIf the download is interrupted or it is detected that some files did not download correctly, you will have two options to resume downloading the missing items:\n1. Respond 'Yes' to a message BiteFX will display on start-up if it detects the download was interrupted.\n2. Tapping the 'Resume Download' button that is displayed in the BiteFX button bar when there are more files to download."];
    
    if(viewUpdates.superview) {
        
        [viewUpdates removeFromSuperview];
    }
    [self.view addSubview:viewDownloadingData];
    
    if(isManageArrayList)
    {
        [self manageArrayForlisting:index];
    }
    NSString *strURL = mutArrSkipDownloadList[intSkipVideoCount][@"FileURL"];
    
    lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of 3 file(s)...",intSkipVideoCount+1];
    progressBar.progress = 0;
    
    NSString *downloadFolder = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"Update0001/"];
    
    NSString *downloadFilename = [downloadFolder stringByAppendingPathComponent:strURL.lastPathComponent];
    NSURL *url1 = [NSURL URLWithString:strURL];
    
    [self.downloadManager download:strURL path:downloadFilename withURL:url1];
    
}
//Code End...

-(void) showTimeOnScroll:(float)time
{
    float timeOriginal = time;
    
    //NSInteger timeSec = (NSInteger) timeOriginal;
    sliderTimeForVideo.value = timeOriginal;
    [UserDefaults setFloat:sliderTimeForVideo.value forKey:@"slidervalue"];
    lastScrollValue = timeOriginal;
    
}

- (void)playerItemDidReachEnd:(NSNotification *)notification {
    
    isPlayerfinished = TRUE;
    
    if(!loopBtn.selected) {
        
        [self SetPlayerToNormalStateWithPause];
        isanimationFinished = TRUE;
        //        [self.m_queueplayer seekToTime:kCMTimeZero];
        [self showTimeOnScroll:sliderTimeForVideo.maximumValue];
    }
    
    if(loopBtn.selected) {
        
        [self.m_queueplayer seekToTime:kCMTimeZero];
        
        //        self.m_queueplayer.actionAtItemEnd = AVPlayerActionAtItemEndNone;
        //        AVPlayerItem *item = [notification object];
        //        [item seekToTime:kCMTimeZero];
        //        NSLog(@"gone in loop selected");
        
    } else if((self.m_queueplayer).currentItem!=nil ) {
        
        //            intcountofVideo++;
        //
        //            NSLog(@" gone in else index = %d",intcountofVideo);
        //            [self playVideoAtIndex:intcountofVideo];
    }
    
}



#pragma mark ------------
#pragma mark TableViewDelegate Methods

-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (tableView.tag == 6)
    {
        return 1;
    }
    else if(tableView.tag == 81)
    {
        return (self.mutArrSkipVideoList).count;
    }
    
    return (self.MutArrUpList).count;
    
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView.tag == 6)
    {
        return 262;
    }
    return 57.0;
}
//-(CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
//    return 100.0;
//}

-(UIView*)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    // [viewUpdates addSubview:viewFortableHeader];
    return nil;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 0;
}

-(UITableViewCell*)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView.tag == 6)
    {
        PurchaseOptionCustomCell *aCell = [PurchaseOptionCustomCell dequeOrCreateInTable:tableView];
        //[aCell setSelectionStyle:UITableViewCellEditingStyleNone];
        aCell.selectionStyle = UITableViewCellSelectionStyleNone;
        
        if (indexPath.row == 0)
        {
            BOOL isEligible = [[NSUserDefaults standardUserDefaults] boolForKey: UD_KEY_ISINTRODUCT_ELIGIBLE];
            
            // If user is eligible for Introductory offer and introductory price is available, show description...
            if (@available(iOS 11.2, *)) {
                if (isEligible && self.product1Month.introductoryPrice.price != nil) {
                    //                aCell.lblPurchaseTitle.text = @"BiteFX on iPad first <numberOfPeriods> month(s) for <introductoryPrice> per month. At end of the introductory period, monthly subscription will be the regular price of <price> per month (unless you cancel).";
                    [self showIntroductoryOfferDetail:aCell];
                } else {
                    aCell.lblPurchaseTitle.text = @"BiteFX on iPad Subscription - Monthly Autorenew";
                    aCell.viewIntroductoryOffer.hidden = YES;
                }
            } else {
                // Fallback on earlier versions
                aCell.lblPurchaseTitle.text = @"BiteFX on iPad Subscription - Monthly Autorenew";
                aCell.viewIntroductoryOffer.hidden = YES;
            }
            
            aCell.btnPurchase.tag = indexPath.row;
            
            NSString *aStrDesc = [NSString stringWithFormat:@"- Use of full set of animations, photographs and presentation templates\n- Regular animation updates (new animations)\n- Periodic software upgrades\n- Recurring subscription which you manage using the \"Manage Your Subscription\" button below"];
            
            aCell.txtViewPurchaseView.text = aStrDesc;
            
            self.btnPurchase1Month = aCell.btnPurchase;
            //            self.btnPurchase6Month = aCell.btnPurchase;
            if ([str1MonthPrice isEqualToString:@""])
            {
                [aCell.actViewpurchase startAnimating];
                aCell.actViewpurchase.hidden = NO;
                aCell.btnPurchase.enabled = NO;
            }
            else
            {
                [aCell.actViewpurchase stopAnimating];
                aCell.actViewpurchase.hidden = YES;
                aCell.btnPurchase.enabled = YES;
                [aCell.btnPurchase setTitle:[NSString stringWithFormat:@"Subscribe for %@ / month", str1MonthPrice] forState:UIControlStateNormal];
            }
            
        }
        else if (indexPath.row == 1)
        {
            aCell.lblPurchaseTitle.text = @"BiteFX on iPad Subscription - Yearly Autorenew";
            
            aCell.btnPurchase.tag = indexPath.row;
            NSString *aStrDesc = [NSString stringWithFormat:@"- Use of full set of animations, photographs and presentation templates\n- Regular animation updates (new animations)\n- Periodic software upgrades\n- Recurring subscription which you manage using the \"Manage Your Subscription\" button below"];
            
            aCell.txtViewPurchaseView.text = aStrDesc;
            
            self.btnPurchase1Year = aCell.btnPurchase;
            if ([str1YearPrice isEqualToString:@""])
            {
                [aCell.actViewpurchase startAnimating];
                aCell.actViewpurchase.hidden = NO;
                aCell.btnPurchase.enabled = NO;
            }
            else
            {
                [aCell.actViewpurchase stopAnimating];
                aCell.actViewpurchase.hidden = YES;
                aCell.btnPurchase.enabled = YES;
                [aCell.btnPurchase setTitle:[NSString stringWithFormat:@"Subscribe for %@ / year", str1YearPrice] forState:UIControlStateNormal];
            }
        }
        
        aCell.delegate = self;
        
        [aCell.btnPurchase setTitleColor:defineWhiteColor forState:UIControlStateNormal];
        
        //        [aCell.btnPruchase addTarget:self action:@selector(onClickPurchaseClose) forControlEvents:UIControlEventTouchUpInside];
        
        return aCell;
    }
    
    else if (tableView.tag == 81)
    {
        skipCell *cell = [skipCell dequeOrCreateInTable:tableView];
        //[cell setSelectionStyle:UITableViewCellSeparatorStyleNone];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        
        cell.btnDownload.hidden = NO;
        (cell.lblTxtdescription).textAlignment = NSTextAlignmentLeft;
        
        lblNoupDates.hidden = YES;
        
        cell.lblVersion.hidden = NO;
        
        if(self.mutArrSkipVideoList.count > 0)
        {
            cell.lblVersion.text = [NSString stringWithFormat:@"%ld",(long)indexPath.row + 1];
            
            
            NSString *atempStr = (self.mutArrSkipVideoList)[indexPath.row][@"FileURL"];
            NSString *atempDownloadStr = [NSString stringWithFormat:@"%@/",DownloadFileURL];
            atempStr = [atempStr stringByReplacingOccurrencesOfString:atempDownloadStr withString:@""];
            NSArray *atempArray = [atempStr componentsSeparatedByString:@"."];
            if(appDelegate.isFullversionDownloading)
            {
                cell.lblTxtdescription.text = atempArray[0];
                
            }
            if(appDelegate.isUpdateDownloading)
            {
                NSString *aTempStrSkip = (self.mutArrSkipVideoList)[indexPath.row][@"FileName"];
                NSArray *aTempSkipVideoName = [aTempStrSkip componentsSeparatedByString:@"."];
                cell.lblTxtdescription.text = aTempSkipVideoName[0];
            }
            
            (cell.btnDownload).tag = indexPath.row;
            [cell.btnDownload addTarget:self action:@selector(btnSkipDownloadClicked:) forControlEvents:UIControlEventTouchUpInside];
            
            cell.textLabel.text = @"";
        }
        cell.accessoryType = UITableViewCellAccessoryNone;
        
        return cell;
    }
    //Code End
    else
    {
        UpdateCell *cell = [UpdateCell dequeOrCreateInTable:tableView];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        (cell.lblTxtdescription).textAlignment = NSTextAlignmentCenter;
        cell.btnDownload.hidden = YES;
        if(isUpdatesChecked)
        {
            lblNoupDates.hidden = YES;
            
            tblViewUpdates.hidden = NO;
            cell.lblDate.hidden = NO;
            cell.lblVersion.hidden = NO;
            cell.txtVdescription.hidden = NO;
            cell.imgLine1.hidden = NO;
            cell.imgLine2.hidden = NO;
            if((self.MutArrUpList).count>0){
                NSString *strStrVersionNumber;
                strStrVersionNumber= [(self.MutArrUpList)[indexPath.row][@"xmlfilePath"] stringByDeletingPathExtension] ;
                
                strStrVersionNumber=[strStrVersionNumber stringByReplacingOccurrencesOfString:@"BiteFXiPadUpdate" withString:@""];
                
                
                cell.lblVersion.text = (self.MutArrUpList)[indexPath.row][@"UpdateVersion"];
                
                //                cell.lblVersion.text = [NSString stringWithFormat:@"%d",strStrVersionNumber.intValue];
                cell.lblDate.text =  (self.MutArrUpList)[indexPath.row][@"updateDate"];
                cell.lblTxtdescription.text = (self.MutArrUpList)[indexPath.row][@"UpdateDescription"];
                
            }
        }
        else
        {
            lblNoupDates.hidden = YES;
            tblViewUpdates.hidden = YES;
            cell.lblDate.hidden = YES;
            cell.lblVersion.hidden = YES;
            cell.txtVdescription.hidden = YES;
            cell.imgLine1.hidden = YES;
            cell.imgLine2.hidden = YES;
            cell.textLabel.textAlignment = NSTextAlignmentCenter;
            
        }
        cell.lblDate.textAlignment = NSTextAlignmentCenter;
        cell.lblVersion.textAlignment = NSTextAlignmentCenter;
        cell.lblTxtdescription.textAlignment = NSTextAlignmentCenter;
        
        // Configure the cell...
        //NSString *continent = [self tableView:tableView titleForHeaderInSection:indexPath.section];
        //NSString *country = [[self.countries valueForKey:continent] objectAtIndex:indexPath.row];
        
        cell.accessoryType = UITableViewCellAccessoryNone;
        
        return cell;
    }
    
    return nil;
}

//===================
// Version 3.0 Changes
//===================
// Show Introductory offer details according to offer type set in Apple account...
- (void)showIntroductoryOfferDetail:(PurchaseOptionCustomCell *)cell {
    
    if (@available(iOS 11.2, *)) {
        
        cell.viewIntroductoryOffer.hidden = NO;
        SKProductDiscountPaymentMode paymentMode = self.product1Month.introductoryPrice.paymentMode;
        NSInteger offerUnits = self.product1Month.introductoryPrice.subscriptionPeriod.numberOfUnits;
        NSString *aStrDuration = [NSString stringWithFormat:@"%ld", offerUnits];
        
        NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc] init];
        numberFormatter.formatterBehavior = NSNumberFormatterBehavior10_4;
        numberFormatter.numberStyle = NSNumberFormatterCurrencyStyle;
        
        numberFormatter.locale = self.product1Month.priceLocale;
        NSString *aStrIntroductoryPrice = [numberFormatter stringFromNumber:self.product1Month.introductoryPrice.price];
        NSString *aStrRegularPrice = [numberFormatter stringFromNumber:self.product1Month.price];
        
        if (paymentMode == SKProductDiscountPaymentModePayAsYouGo) {
            cell.lblPurchaseTitle.text = PAY_AS_YOU_GO_TITLE;
            NSString *aStrDescription = [NSString stringWithFormat:PAY_AS_YOU_GO_DESCRIPTION, aStrDuration, aStrIntroductoryPrice, aStrRegularPrice];
            cell.lblIntroductoryOffer.text = aStrDescription;
            
        } else if (paymentMode == SKProductDiscountPaymentModePayUpFront) {
            cell.lblPurchaseTitle.text = PAY_UP_FRONT_TITLE;
            NSString *aStrDescription = [NSString stringWithFormat:PAY_UP_FRONT_DESCRIPTION, aStrDuration, aStrIntroductoryPrice, aStrRegularPrice];
            cell.lblIntroductoryOffer.text = aStrDescription;
        } else if (paymentMode == SKProductDiscountPaymentModeFreeTrial) {
            cell.lblPurchaseTitle.text = FREE_TRIAL_TITLE;
            NSString *aStrDescription = [NSString stringWithFormat:FREE_TRIAL_DESCRIPTION, aStrDuration, aStrRegularPrice];
            cell.lblIntroductoryOffer.text = aStrDescription;
        }
        
    }
}

#pragma mark-
#pragma  mark custom play method
//arrPlayerItems
- (void) playVideoAtIndex:(NSInteger)index {
    
    //    NSArray *arrNewVideos = @[@"JAMSS", @"JAMSS", @"JAMSS-8KF", @"JAMSS-16KF", @"JAMSS-32KF", @"JAMSS-63KF", @"JAMSS-119KF"];
    //    NSArray *arrNewVideos = @[@"GoodOpenCloseOCSide", @"GoodContactAllQtr4Views", @"GoodAntRotateSideFront", @"BadGrindSideMusclesCU", @"BadGoodContactFront", @"BadGoodAntMolarChip", @"SlideNoMuscle"];
    //    NSString *aExt = @".mp4";
    
    //    if (index == 0) {
    //        aExt = @".mov";
    //    }
    //    NSString *videoName = arrNewVideos[index];
    //    [self test:videoName andExtension:aExt];
    isanimationFinished=FALSE;
    
    NSLog(@"selected Video: %@", arrPlayerItems[index]);
    selectedVideoToPlayOnFullScreen = arrPlayerItems[index];
    NSURL *aURL = [NSURL fileURLWithPath:arrPlayerItems[index]];
    AVPlayerItem *videoItem = [AVPlayerItem playerItemWithURL:aURL];
    printf("aURL:::%s", aURL);
    [self getVideoMediaTimeScale:aURL];
    
    /*
     /// Temporary logic for h265 videos.
     if (arrPlayerItems.count == 7) {
     
     // Get new video path
     NSString *aStr = [[NSBundle mainBundle] pathForResource:videoName ofType:aExt];
     videoItem = [AVPlayerItem playerItemWithURL:[NSURL fileURLWithPath:aStr]];
     }
     */
    self.m_queueplayer = [AVQueuePlayer queuePlayerWithItems:@[videoItem]];
    (self.m_playerView).player = self.m_queueplayer;
    
    asset_ = [AVURLAsset URLAssetWithURL:[NSURL fileURLWithPath:arrPlayerItems[index]] options:nil];
    
    [asset_ loadValuesAsynchronouslyForKeys:@[@"tracks"] completionHandler:^{
        NSArray *  tracks = [self->asset_ tracksWithMediaType:AVMediaTypeVideo];
        
        if(tracks.count == 0) {
            NSLog(@"%s asset does not contain a video trac.", __func__);
            return ;
        }
    }];
    
    NSArray *  tracks = [asset_ tracksWithMediaType:AVMediaTypeVideo];
    if(tracks.count == 0) {
        
        NSLog(@"%s asset does not contain a video trac..", __func__);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:@"Video did not complete download!" cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        });
        
        return;
    }
    
    AVAssetTrack *videoTrack = tracks[0];
    float  frameRate = videoTrack.nominalFrameRate;
    CMTime durationTime = self.m_queueplayer.currentItem.asset.duration;
    
    float time = CMTimeGetSeconds(durationTime);
    [UserDefaults setFloat:time forKey:@"videoTime"];
    [UserDefaults setFloat:(frameRate*time) forKey:@"frames"];
    [UserDefaults synchronize];
    
    floatCount = time/frameRate;
    sliderTimeForVideo.minimumValue = 0.0;
    sliderTimeForVideo.maximumValue = time;
    sliderTimeForVideo.value = 0.0;
    //    self.m_queueplayer.rate= self.sliderSpeedControl.value;
    
    Speed = 1.0;
    if(appDelegate.mutArrPlayVideo.count > currentIndex)
    {
        NSMutableDictionary *objDict = appDelegate.selectedObj != nil ? (appDelegate.arrMedia)[0] : (appDelegate.mutArrPlayVideo)[currentIndex];
        
        NSString *spdVal = objDict[@"moviePlaySpeed"];
        
        if([spdVal isEqualToString:@""] || [spdVal isEqualToString:@"0.0"] || [spdVal.lowercaseString containsString:@"null"] || [spdVal.lowercaseString containsString:@"nil"] || spdVal == nil)
        {
            spdVal = @"1.0";
        }
        Speed = spdVal.floatValue;
        self.sliderSpeedControl.value = Speed;
        
        NSString *favourite = [NSString stringWithFormat:@"%@",appDelegate.selectedObj != nil ? (appDelegate.arrMedia)[0][@"isFavourite"] : (appDelegate.mutArrPlayVideo)[currentIndex][@"isFavourite"]];
        [btnFavUnfav setSelected:[favourite isEqualToString:@"1"]];
    }
    else {
        Speed = 1.0;
    }
    btn_SpeedControl.alpha = 1.0f;
    
    if(isnextClicked||isPreviousClicked) {
        
        [self.m_queueplayer pause];
        [self setPlayPauseImageForNormalState];
        
    } else {
        
        self.m_queueplayer.rate = Speed;
        [self.m_queueplayer play];
    }
    NSLog(@"End of function play video");
}

- (void)getVideoMediaTimeScale:(NSURL *)aURL {
    
    AVURLAsset *asset = [AVURLAsset assetWithURL:aURL];
    
    [asset loadValuesAsynchronouslyForKeys:@[@"playable", @"hasProtectedContent", @"tracks"] completionHandler:^{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if ([self validateValues:@[@"playable", @"hasProtectedContent", @"tracks"] forAsset:asset]) {
                
                AVPlayerItem *playerItem = [AVPlayerItem playerItemWithAsset:asset];
                NSArray *arrTracks =  playerItem.tracks;
                for (AVPlayerItemTrack *track in arrTracks) {
                    
                    AVAssetTrack *assetTrack = track.assetTrack;
                    if (assetTrack != nil) {
                        //                        if (assetTrack.mediaType == AVMediaTypeVideo) {
                        self.mediaTimeScale = assetTrack.naturalTimeScale;
                        NSLog(@"natural time scale: %d", self.mediaTimeScale);
                        //                        }
                    } else {
                        continue;
                    }
                    
                }
                
            }
        });
    }];
    
}

- (void)test:(NSString *)name andExtension:(NSString *)ext {
    
    NSURL *aURL = [[NSBundle mainBundle] URLForResource:name withExtension:ext];
    AVURLAsset *asset = [AVURLAsset assetWithURL:aURL];
    
    [asset loadValuesAsynchronouslyForKeys:@[@"playable", @"hasProtectedContent", @"tracks"] completionHandler:^{
        
        dispatch_async(dispatch_get_main_queue(), ^{
            
            if ([self validateValues:@[@"playable", @"hasProtectedContent", @"tracks"] forAsset:asset]) {
                
                AVPlayerItem *playerItem = [AVPlayerItem playerItemWithAsset:asset];
                NSArray *arrTracks =  playerItem.tracks;
                for (AVPlayerItemTrack *track in arrTracks) {
                    
                    AVAssetTrack *assetTrack = track.assetTrack;
                    if (assetTrack != nil) {
                        //                        if (assetTrack.mediaType == AVMediaTypeVideo) {
                        self.mediaTimeScale = assetTrack.naturalTimeScale;
                        NSLog(@"natural time scale: %d", self.mediaTimeScale);
                        //                        }
                    } else {
                        continue;
                    }
                    
                }
                
            }
        });
    }];
    
}

- (BOOL)validateValues:(NSArray *)arrKeys forAsset:(AVAsset*)newAsset {
    
    for (NSString *aStrKey in arrKeys) {
        NSError *error;
        if([newAsset statusOfValueForKey:aStrKey error:&error] == AVKeyValueStatusFailed) {
            NSLog(@"You can't use this AVAsset because one of it's keys failed to load.");
            return  false;
        }
    }
    
    if (!newAsset.isPlayable || newAsset.hasProtectedContent) {
        /*
         You can't play the asset. Either the asset can't initialize a
         player item, or it contains protected content.
         */
        NSLog(@"You can't use this AVAsset because one of it's keys failed to load.");
        return false;
    }
    return true;
}

#pragma mark -
#pragma mark LabelValueChangeaccordingVideo

- (void)labelValueChange {
    
    //    NSLog(@"called");
    float framestotal = [UserDefaults floatForKey:@"frames"];
    
    float sliderval  = sliderTimeForVideo.value; // [UserDefaults floatForKey:@"slidervalue"];
    currentFrame = (int)((framestotal*sliderval)/sliderTimeForVideo.maximumValue);
    
    if(currentFrame<1) {
        currentFrame = 1;
    }
    
    lblFramcounter.text = [NSString stringWithFormat:@"%d",currentFrame];
    currentFrame = 1;
    //[UserDefaults removeObjectForKey:@"frames"];
}

#pragma mark Popupmenu actions

- (IBAction)PurchaseAction:(id)sender {
    // New Version 3.0 changes...
    // Call verifyEligibilityV5 API to check user is eligible for Introductory offer or not...
    [self callWSForVerifyEligibility];
}

- (void)callWSForVerifyEligibility {
    
    [SVProgressHUD show];
    
    NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
    body[@"method"] = VERIFY_ELIGIBILITY;
    
    NSString *strReceipt = [Utility getInAppPurchaseReceipt];
    if ([strReceipt isEqualToString:@""]) {
        NSLog(@"No Receipt");
        [self refreshReceipt];
        return;
    } else {
        body[@"receipts"] = @[strReceipt];
    }
    
    WebService *aWebService = [[WebService alloc] init];
    [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
        
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            [SVProgressHUD dismiss];
            return;
        }
        NSDictionary *aDictResponse = object;
        [self checkEligibility:aDictResponse];
    }];
    
}

-(void)checkEligibility:(NSDictionary *)responsedictionary
{
    int intStatus = [responsedictionary[@"status"] intValue];
    int intStatusCode = [responsedictionary[@"status_code"] intValue];
    NSString *aStrResponse = responsedictionary[@"message"];
    
    [SVProgressHUD dismiss];
    
    // Forsuccess StatusCode is 105...
    if (intStatus == 1 && intStatusCode == 105) {
        
        NSString *introEligible = responsedictionary[@"eligible_for_intro_offer"];
        if([introEligible.lowercaseString isEqualToString:@"yes"]) {
            [UserDefaults setBool:YES forKey:UD_KEY_ISINTRODUCT_ELIGIBLE];
        } else {
            [UserDefaults setBool:NO forKey:UD_KEY_ISINTRODUCT_ELIGIBLE];
        }
        [UserDefaults synchronize];
        
    }
    else
    {
        NSString *msg = strAlertBiteFXSupport;
        if (aStrResponse.length > 0)
        {
            msg = aStrResponse;
        }
        // SCM - 2023-06-07 Error message now captures current action at time of error and status code returned by server.
        msg = [ErrorFormatter formatErrorMsgWithBaseString:msg errorType:CHECK_ELIGIBILITY_ERROR serverReturnCode:intStatusCode];
        
        if (intStatusCode == 400) {
            [self checkForError400:msg];
        }
        else {
            
            // Display the error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:msg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        
    }
    
    // Show purchase popup...
    dispatch_async(dispatch_get_main_queue(), ^{
        [self showPurchasePopup];
    });
    
}

- (void)showPurchasePopup {
    [self NoInternetConnectionShowAlert];
    
    viewPurchaseDetails.hidden = YES;
    webViewPurchaseDetails.hidden = YES;
    
    // Version 3.0 Changes
    // Show Manage SUbscription option for already subscribed user...
    if (userSubStat == InAppSubscribed) {
        tblViewPurchaseOption.hidden = YES;
        CGRect frame = viewSubscriptionOptions.frame;
        frame.origin.y = 140;
        viewSubscriptionOptions.frame = frame;
    } else {
        tblViewPurchaseOption.hidden = NO;
    }
    
    btnProducts.hidden = YES;
    lblPurchaseOptions.text = @"BiteFX Purchase Options";
    viewPurchaseDetails.frame = CGRectMake(91, 139, 748, 504);
    webViewPurchaseDetails.frame = CGRectMake(0, 0, viewPurchaseDetails.frame.size.width, viewPurchaseDetails.frame.size.height);
    
    //===================
    // Version 3.0 Changes
    //===================
    // New InApp Code changes...
    [[IAPManager sharedInstance] requestProducts:@[InApp_One_Month_Product_ID] completion:^(NSMutableArray * _Nonnull products, NSError * _Nullable aError) {
        
        if (aError) {
            // Show error message...
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            return;
        }
        
        if (products.count > 0 ) {
            
            for (SKProduct *objProduct in products) {
                if ([objProduct.productIdentifier isEqualToString:InApp_One_Month_Product_ID]) {
                    self->str1MonthPrice = [[IAPManager sharedInstance] getLocalPriceForProduct:objProduct];
                    self.product1Month = objProduct;
                }
            }
            
            dispatch_async(dispatch_get_main_queue(), ^{
                [self->tblViewPurchaseOption reloadData];
            });
        }
        
    }];
    
    [webViewPurchaseDetails loadRequest:[NSURLRequest requestWithURL:[NSURL fileURLWithPath:[[NSBundle mainBundle] pathForResource:@"BiteFXiPad-Subscription" ofType:@"html"]]]];
    
    [self setMenuButtonNormal];
    [self removeviewPopupFromSuperView];
    
    [self.view addSubview:viewPurchase];
}


- (IBAction)RegisterAction:(id)sender {
    
    [self NoInternetConnectionShowAlert];
    
    [self setMenuButtonNormal];
    [self removeviewPopupFromSuperView];
    
    // Register for keyboard notifications to handle keyboard show/hide
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
    
    [self.view addSubview:viewRegistration];
    // self.txtFieldEmail.text=@"";
    //[self.view bringSubviewToFront:viewRegistration];
    
    if (BtnRegistration.enabled)
    {
        isRegistered = NO;
    }
    else
    {
        isRegistered = YES;
    }
    
    if (isRegistered)
    {
        
        if (!BtnRegistration.enabled)
        {
            self.txtFieldEmail.userInteractionEnabled = FALSE;
            txtFieldSerialnumber1.userInteractionEnabled = FALSE;
            btnRegister.enabled = NO;
            isRegistered = YES;
        }
        else
        {
            self.txtFieldEmail.userInteractionEnabled = TRUE;
            txtFieldSerialnumber1.userInteractionEnabled = TRUE;
            btnRegister.enabled = YES;
            isRegistered = NO;
        }
        
        
        
        [UserDefaults setObject:self.strEmail forKey:@"Email"];
        [UserDefaults setObject:self.strSerialNumber forKey:@"SerialNumber"];
        [UserDefaults synchronize];
        self.txtFieldEmail.text = self.strEmail;
        //txtFieldEmail.text = @"<EMAIL>";
        
        txtFieldSerialnumber1.text = self.strSerialNumber ;
        //txtFieldSerialnumber1.text = @"1234";
        
    }
    else
    {
        self.txtFieldEmail.userInteractionEnabled = TRUE;
        txtFieldSerialnumber1.userInteractionEnabled = TRUE;
        
        
        
        btnRegister.frame  = CGRectMake(325, 375, 89, 36);
        self.strEmail = [UserDefaults objectForKey:@"Email"];
        self.strSerialNumber = [UserDefaults objectForKey:@"SerialNumber"];
        btnRegister.enabled = YES;
    }
    
}
//update button clickEvent
-(IBAction)updatesAction:(id)sender
{
    [self NoInternetConnectionShowAlert];
    
    [self setMenuButtonNormal];
    [self removeviewPopupFromSuperView];
    
    tblViewSkipUpdate.hidden = YES;
    lblSubtitle.hidden = NO;
    btnCheckUpdate.hidden = NO;
    lblUpdatesAvailble.hidden = NO;
    btnDownloadUpdate.hidden = NO;
    tblViewUpdates.hidden = NO;
    
    //lastCheckForUpdate
    NSDate *date= [UserDefaults  valueForKey:@"lastCheckForUpdate"];
    if (date!=nil) {
        lblSubtitle.text = [NSString stringWithFormat:@"Last check for updates: %@",[dateFormatter stringFromDate:date]];
    }else{
        lblSubtitle.text = @"Last check for updates: -";
    }
    
    [self.view addSubview:viewUpdates];
    
    [self versionCheck];
    if([UserDefaults boolForKey:@"UpdateDownloaded"])
    {
        
        if(intupdate>0)
        {
            
            tblViewUpdates.hidden = FALSE;
            [tblViewUpdates reloadData];
            imgUpdatesAvailable.hidden = NO;
            lblUpdatesavailable.hidden = NO;
            lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",intupdate];
            lblMenuUpdates.hidden = NO;
            lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
            
            tblViewUpdates.hidden = NO;
            lblNoupDates.hidden = NO;
            lblNoupDates.text = @"";
            
        }
        else
        {
            imgUpdatesAvailable.hidden = YES;
            lblUpdatesavailable.hidden = YES;
            lblMenuUpdates.hidden = YES;
            tblViewUpdates.hidden = YES;
            lblNoupDates.hidden = NO;
            lblNoupDates.text = @"No updates available";
        }
        
    }
    else
    {
        
        if(AppDelegateobj.connected)
        {
            if (userSubStat == Subscribed)
            {
                [self callUpdateCheckWebservice:nil];
            }
            else
            {
                [self callUpdateCheckForInAppWebservice:nil];
            }
            
            tblViewUpdates.hidden = YES;
            lblNoupDates.hidden = YES;
            [tblViewUpdates reloadData];
        }
        else
        {
            // tag : 0
            [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:ErrorMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
    }
    //  [self.view bringSubviewToFront:viewUpdates];
}
- (IBAction)Aboutaction:(id)sender {
    [self setMenuButtonNormal];
    [self removeviewPopupFromSuperView];
    [self.view addSubview:viewAboutBitefx];
    
    viewAboutBasic.hidden = YES;
    viewAboutLogin.hidden = YES;
    viewAboutSubscription.hidden = YES;
    
    if (userSubStat == Subscribed) {
        viewAboutLogin.hidden = NO;
        lblLicenceEmail.text = [NSString stringWithFormat:@"Active BiteFX Member: %@", [UserDefaults objectForKey:@"email"]];
    } else if (userSubStat == InAppSubscribed) {
        viewAboutSubscription.hidden = NO;
    } else {
        viewAboutBasic.hidden = NO;
    }
    
    NSArray *arrStrings = @[@"Using the Purchase option to start a BiteFX autorenewable subscription through the iTunes Store, or",
                            @"If you already have an active autorenewable subscription, use the Restore Purchase option on the Purchase dialog, or",  @"If you are a BiteFX Platinum or Gold member, use the Login function with your BiteFX ID and login password."];
    
    lblAboutBasic.attributedText = [self attributedStringForBulletTexts:arrStrings withFont:lblAboutBasic.font bulletString:@"o" indentation:15                                               lineSpacing:2 paragraphSpacing:2 textColor:UIColor.whiteColor bulletColor:UIColor.whiteColor];
    
}

- (IBAction)btnStopDownload:(id)sender {
    
    //NSLog(@"%d %d",[UserDefaults boolForKey:@"FullDownloaded"],UpdateCompleted);
    if(appDelegate.isSkipVideoFullVerison)
    {
        lblProgresspercent.hidden = YES;
        if(viewDownloadingData)
        {
            [viewDownloadingData removeFromSuperview];
        }
        appDelegate.isSkipVideoFullVerison = NO;
        [self.downloadManager cancel];
        [self.downloadManager cleanup];
        return;
    }
    if(appDelegate.isSkipVideoUpdateVerison)
    {
        lblProgresspercent.hidden = YES;
        if(viewDownloadingData)
        {
            [viewDownloadingData removeFromSuperview];
        }
        appDelegate.isSkipVideoUpdateVerison = NO;
        [self.downloadManager cancel];
        [self.downloadManager cleanup];
        return;
    }
    
    if ((![UserDefaults boolForKey:@"FullDownloaded"]&& UpdateCompleted ==FALSE) ||  ([UserDefaults boolForKey:@"FullDownloaded"] && UpdateCompleted==FALSE)) {
        
        // tag 5
        [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:@"The download hasn't completed yet.\n Are you sure you wish to abort the download?" cancelButtonTitle:@"YES" destructiveButtonTitle:nil otherButtonTitles:@[@"NO"] tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
            [self dismissViewControllerAnimated:YES completion:nil];
            [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
            if(buttonIndex == controller.cancelButtonIndex)
            {
                [UIApplication sharedApplication].idleTimerDisabled = NO;
                [self->btnMenu setEnabled:YES];
                AppDelegateobj.isDownloadCancelled = TRUE;
                
                if (AppDelegateobj.isUpdateDownloading) {
                    
                    self->isUpdateInterrupted = 1;
                }
                
                if (self->arrFileDownloadList.count > 0) {
                    
                    if (AppDelegateobj.isUpdateDownloading) {
                        
                        if (self->isUpdateInterrupted == 1) {
                            self->btnRemainDownload.hidden = NO;
                        } else {
                            self->btnRemainDownload.hidden = YES;
                        }
                    } else {
                        
                        self->btnRemainDownload.hidden = NO;
                    }
                    //btnRemainDownload.hidden = NO;
                } else {
                    self->btnRemainDownload.hidden = YES;
                }
                
                if(self->viewDownloadingData)
                {
                    [self->viewDownloadingData removeFromSuperview];
                }
                [self.downloadManager cancel];
                [self.downloadManager cleanup];
                [UserDefaults  setBool:YES forKey:@"isDowmloadCancelled"];
                [UserDefaults  synchronize];
                
                
                self->alertForUpdateMessage = [UIAlertController alertControllerWithTitle:ErrorTitle message:UPDATE_SUCCESS_MESSAGE preferredStyle:UIAlertControllerStyleAlert];
                [self presentViewController:self->alertForUpdateMessage animated:YES completion:nil];
                
                // Version 2.5.1 change - Comment code of background thread to solve one flag (isFullVersionXML)issue...
                //                dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                
                // Download Full Version MVI and save data...
                [self downloadFullVersionMVIAndSaveData];
                //                });
            }
        }];
        
    } else {
        
        lblProgresspercent.hidden = YES;
        if(viewDownloadingData)
        {
            [viewDownloadingData removeFromSuperview];
        }
        
        // All files are downloaded.
        // Version 3.0 changes
        // To refresh UI in AnimationPanel after downloading all files.
        // This is needed because we enable Menu button as per client requirement.
        // Post Notification that all files are downloaded.
        appDelegate.intSelectedTab = 0;
        [[NSNotificationCenter defaultCenter] postNotificationName:REFRESH_ANIMATIOM_PANEL_DATA object:nil];
    }
}
- (void)hideFrameButton {
    
    [UIView beginAnimations:nil context:NULL];
    [UIView setAnimationDuration:1.0];
    [UIView commitAnimations];
}

- (IBAction)info:(id)sender {
    
    if (appDelegate.isPresentationInfoOpen) {
        [animation.viewPresentationInfo removeFromSuperview];
    }
    
    [self setHelpButtonNormal];
    [self setMenuButtonNormal];
    //Removed some code when animation panel selected. //This case never execute. on 10th Jun 2017
    if(!btnInfo.selected)
    {
        btnInfo.selected=YES;
        isinfoHtmlLoaded=YES;
        m_playerView.frame = CGRectMake(324, 0, 605, 696);
        m_playerView.contentMode=UIViewContentModeScaleAspectFit;
        [self animation];
        [self LoadWbViewWithURL];
        [self.view addSubview:wbView];
        lbl_view_title.frame = CGRectMake(324, 0 , 605 , 53);
    }
    else
    {
        [self removeWebView];
        
        //===================
        // Version 2.4 Changes
        //===================
        if (appDelegate.intSelectedTab == 2 && appDelegate.isPresentationInfoOpen) {
            
            // If Presentation Info is opened, before animation info open, show it again...
            [self showPresentationInfo];
        }
    }
}

-(void)removeWebView
{
    btnInfo.selected=NO;
    isinfoHtmlLoaded=NO;
    m_playerView.frame = CGRectMake(0, 0, 929, 696);
    m_playerView.contentMode=UIViewContentModeScaleToFill;
    [wbView removeFromSuperview];
    
    imgview_fullImage.frame = CGRectMake(0, 0, 929, 696);
    
    if (appDelegate.isPresentationInfoOpen) {
        lbl_view_title.frame = CGRectMake(324, 0 , 605 , 53);
    }
    else
    {
        lbl_view_title.frame = CGRectMake(0, 0 , 929 , 53);
    }
    
}

- (void)showPresentationInfo {
    
    if (appDelegate.isPresentationInfoOpen) {
        
        // Adjust UI...
        animation.viewPresentationInfo.frame = CGRectMake(0, 0, PRESENTATION_INFO_VIEW_WIDTH, 695);
        
        for (UIView *aView in animation.viewPresentationInfo.subviews) {
            
            if ([aView isKindOfClass:[InfoWebView class]]) {
                
                // Adjust Webview frame...
                aView.frame = CGRectMake(0, 30, PRESENTATION_INFO_VIEW_WIDTH, 665);
                
                for (UIView *aSubView in aView.subviews) {
                    
                    if ([aSubView isKindOfClass:[WKWebView class]]) {
                        
                        // Adjust Webview frame...
                        aSubView.frame = CGRectMake(0, 0, PRESENTATION_INFO_VIEW_WIDTH, 665);
                    }
                }
                
            }
        }
        
        [self.view addSubview:animation.viewPresentationInfo];
        [animation.btnClose addTarget:self action:@selector(btnCloseInfoClick:) forControlEvents:UIControlEventTouchUpInside];
        
        m_playerView.frame = CGRectMake(324, 0, 605, 696);
        m_playerView.contentMode=UIViewContentModeScaleAspectFit;
        imgview_fullImage.frame = CGRectMake(324, 0, 605, 696);
        
    }
    
}

//- (void)showPresentationInfo {
//
//    if (appDelegate.isPresentationInfoOpen) {
//
//        // Adjust UI...
//        [wbView loadRequestWithURL:animation.strSelectedPresentationInfoPath];
//
//        animation.viewPresentationTitle.frame = CGRectMake(0, 0, PRESENTATION_INFO_VIEW_WIDTH, 30);
//        [self.view addSubview:animation.viewPresentationTitle];
//        [self.view addSubview:wbView];
//
////        [self.view addSubview:self.viewPresentationInfo];
//
//        CGRect frame = animation.viewPresentationInfo.frame;
//        frame.origin.y = 0;
//        frame.size.height = frame.size.height + 35;
//
//        animation.viewPresentationInfo.frame = frame;
//
//        for (UIView *aView in animation.viewPresentationInfo.subviews) {
//
//            if ([aView isKindOfClass:[InfoWebView class]]) {
//
//                // Adjust Webview frame...
//                frame = aView.frame;
//                frame.size.height = frame.size.height + 35;
//                aView.frame = frame;
//            }
//        }
//
//        [self.view addSubview:animation.viewPresentationInfo];
//
//        m_playerView.frame = CGRectMake(324, 0, 605, 696);
//        m_playerView.contentMode=UIViewContentModeScaleAspectFit;
//        lbl_view_title.frame = CGRectMake(324, 0 , 605 , 53);
//    }
//}

- (IBAction)btnCloseInfoClick:(UIButton *)sender {
    
    appDelegate.isPresentationInfoOpen = NO;
    [animation.viewPresentationInfo removeFromSuperview];
    
    [self removeWebView];
    
    // Reset UI...
    //    m_playerView.frame = CGRectMake(0, 0, 929, 696);
    //    m_playerView.contentMode=UIViewContentModeScaleToFill;
    //    lbl_view_title.frame = CGRectMake(0, 0 , 929 , 53);
    
}

- (void)animation {
    
    CATransition *animation1 = [CATransition animation];
    animation1.delegate = self;
    animation1.duration = 1.0f;
    NSString *direction;
    //    [animation1 setTimingFunction:kCAMediaTimingFunctionEaseOut];
    animation1.type = kCATransitionFromRight;
    direction = kCATransitionFromRight;
    animation1.subtype = direction;
    [animation1 setRemovedOnCompletion:YES];
    animation1.fillMode = @"extended";
    [animation1 setRemovedOnCompletion: YES];
}
- (IBAction)help:(id)sender {
    [self SetPlayerToNormalStateWithPause];
    if (view_Back)
    {
        [view_Back removeFromSuperview];
    }
    
    btnHelp.selected =  !btnHelp.selected;
    
    //    if(btnInfo.selected)
    //    {
    //        btnInfo.selected=NO;
    //    }
    [self setMenuButtonNormal];
    btnHelpView.hidden=NO;
    //    btnHelpView.userInteractionEnabled = NO;
    btnHelpClose.hidden = NO;
    
    btnHelpClose.frame = CGRectMake(btnHelpView.frame.size.width-95, btnHelp.frame.origin.y, btnHelp.frame.size.width, btnHelp.frame.size.height);
    [self.view.superview addSubview:btnHelpClose];
    
    
    //m_playerView.frame = CGRectMake(0, 0, 929, 696);
    if (btnAnimationPanel.selected)
    {
        if (appDelegate.intSelectedTab == 0) {
            
            [btnHelpView setImage:[UIImage imageNamed:@"Selection_panel_with animations_tab_selected.png"] forState:UIControlStateNormal];
        }
        else if (appDelegate.intSelectedTab == 1) {
            
            [btnHelpView setImage:[UIImage imageNamed:@"Selection_panel_with_pictures_tab_selected.png"] forState:UIControlStateNormal];
        }
        else
        {
            [btnHelpView setImage:[UIImage imageNamed:@"Selection_panel_with_presentation_templates_tab_selected.png"] forState:UIControlStateNormal];
        }
        
    }
    else
    {
        if (isImageDisplay) {
            
            [btnHelpView setImage:[UIImage imageNamed:@"Main_screen_with_picture_displayed.png"] forState:UIControlStateNormal];
        }
        else
        {
            [btnHelpView setImage:[UIImage imageNamed:@"Main_screen_with_animation_displayed.png"] forState:UIControlStateNormal];
        }
        
    }
    
    [btnHelp setImage:[UIImage imageNamed:@"Help.png"] forState:UIControlStateNormal];
    [self.view addSubview:btnHelpView];
    [self manageUiEvents];
    
    //    if(!btnHelp.selected)
    //    {
    //        btnHelp.selected=YES;
    //        m_playerView.frame = CGRectMake(325, 0, 604, 696);
    //
    //
    //        NSString *path;
    //        NSBundle *thisBundle = [NSBundle mainBundle];
    //
    //        webView.backgroundColor = [UIColor colorWithRed:148.0/256.0 green:148.0/256.0 blue:148.0/256.0 alpha:0.0];
    //        webView.opaque = YES;
    //        path = [thisBundle pathForResource:@"BiteFXWelcomeAndHelp" ofType:@"html"];
    //
    //        NSURL *instructionsURL = [[NSURL alloc] initFileURLWithPath:path];
    //        [webView loadRequest:[NSURLRequest requestWithURL:instructionsURL]];
    //    }
    //    else
    //    {
    //        btnHelp.selected=NO;
    //
    //
    //        m_playerView.frame = CGRectMake(0, 0, 929, 696);
    //
    //    }
    
    //    NSURL *url = [NSURL URLWithString:@"http://www.bitefx.com/support.php"];
    //    NSURLRequest *request = [NSURLRequest requestWithURL:url];
    
}
- (void)CheckingUpdates {
    
    int aIntRegistered = [UserDefaults boolForKey:@"Registered"];
    if(aIntRegistered)
    {
        int aIntUpdate = [UserDefaults boolForKey:@"UpdateDownloaded"];
        if(aIntUpdate) {
            
            if(intupdate>0) {
                
                imgMenuUpdates.hidden = NO;
                imgUpdatesAvailable.hidden = NO;
                lblUpdatesavailable.hidden  = NO;
                lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",intupdate];
                lblMenuUpdates.hidden = NO;
                lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
            } else {
                imgMenuUpdates.hidden = YES;
                lblUpdatesavailable.hidden = YES;
                lblMenuUpdates.hidden = YES;
                imgUpdatesAvailable.hidden = YES;
                
            }
            
        } else {
            
            if(intupdate>0) {
                
                imgMenuUpdates.hidden = NO;
                imgUpdatesAvailable.hidden = NO;
                lblUpdatesavailable.hidden  = NO;
                lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",intupdate];
                lblMenuUpdates.hidden = NO;
                lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
            }
            else {
                imgMenuUpdates.hidden = YES;
                lblUpdatesavailable.hidden = YES;
                lblMenuUpdates.hidden = YES;
                imgUpdatesAvailable.hidden = YES;
            }
        }
    }
    else
    {
        if(intupdate>0)
        {
            imgMenuUpdates.hidden = NO;
            imgUpdatesAvailable.hidden = NO;
            lblUpdatesavailable.hidden  = NO;
            lblUpdatesavailable.text =[NSString stringWithFormat:@"%d",intupdate];
            lblMenuUpdates.hidden = NO;
            lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
        }
        else
        {
            imgMenuUpdates.hidden = YES;
            lblUpdatesavailable.hidden = YES;
            lblMenuUpdates.hidden = YES;
            imgUpdatesAvailable.hidden = YES;
        }
    }
}

- (void) setUpdateCount {
    
    if(intupdate > 0){
        lblMenuUpdates.hidden = NO;
        lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
        imgMenuUpdates.hidden = NO;
    }
    else{
        lblMenuUpdates.hidden = TRUE;
        imgMenuUpdates.hidden = TRUE;
    }
}
-(void)versionCheck {
    NSDictionary* infoDictionary = [NSBundle mainBundle].infoDictionary;
    NSString* appID = infoDictionary[@"CFBundleIdentifier"];
    NSURL* url = [NSURL URLWithString:[NSString stringWithFormat:@"http://itunes.apple.com/lookup?bundleId=%@", appID]];
    NSData* data = [NSData dataWithContentsOfURL:url];
    NSDictionary* lookup = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
    
    if ([lookup[@"resultCount"] integerValue] == 1  ){
        NSString* appStoreVersion = lookup[@"results"][0][@"version"];
        NSString* currentVersion = infoDictionary[@"CFBundleShortVersionString"];
        if (![appStoreVersion isEqualToString:currentVersion]){
            NSLog(@"Need to update [%@ != %@]", appStoreVersion, currentVersion);
            lblVersion.text = [NSString stringWithFormat:@"Latest version (%@) : Current version (%@)",appStoreVersion,currentVersion];
            lblVersion.textColor = defineRedColor;
        }else{
            lblVersion.text = [NSString stringWithFormat:@"Latest version (%@) : Up to date",appStoreVersion];
            lblVersion.textColor = defineBlackColor;
        }
    }
}
- (IBAction)menu:(id)sender {
    //    btnInfo.selected=NO;
    //    m_playerView.contentMode=UIViewContentModeScaleToFill;
    //    isinfoHtmlLoaded=NO;
    //    m_playerView.frame = CGRectMake(0, 0, 929, 696);
    //    [wbView removeFromSuperview];
    
    
    // Version 3.0 Changes
    // Enable Purchase bitton for subscribed user...
    // Show Manage SUbscription option for already subscribed user...
    if (userSubStat == Subscribed) {
        
        BtnRegistration.enabled = NO;
        [BtnRegistration setTitleColor:defineGrayColor forState:UIControlStateNormal];
        
        //btnSkip.enabled = YES;
        //[btnSkip setTitleColor:defineBlackColor forState:UIControlStateNormal];
        
        btnPurchase.enabled = NO;
        [btnPurchase setTitleColor:defineGrayColor forState:UIControlStateNormal];
        
        btnUpdate.enabled = YES;
        [btnUpdate setTitleColor:defineBlackColor forState:UIControlStateNormal];
        
    } else if (userSubStat == InAppSubscribed) {
        
        BtnRegistration.enabled = NO;
        [BtnRegistration setTitleColor:defineGrayColor forState:UIControlStateNormal];
        
        //btnSkip.enabled = YES;
        //[btnSkip setTitleColor:defineBlackColor forState:UIControlStateNormal];
        
        // Version 3.0 changes
        btnPurchase.enabled = YES;
        
        btnUpdate.enabled = YES;
        [btnUpdate setTitleColor:defineBlackColor forState:UIControlStateNormal];
    }
    else
    {
        // Enable Registration and Purchase button...
        BtnRegistration.enabled = YES;
        [BtnRegistration setTitleColor:defineBlackColor forState:UIControlStateNormal];
        
        btnPurchase.enabled = YES;
        [btnPurchase setTitleColor:defineBlackColor forState:UIControlStateNormal];
        
        btnUpdate.enabled = NO;
        [btnUpdate setTitleColor:defineGrayColor forState:UIControlStateNormal];
        //btnUpdate.titleLabel.textColor = defineGrayColor;
        //        btnPurchase.enabled = YES;
        //        BtnRegistration.enabled = YES;
    }
    NSString *aTempStr;
    NSMutableArray *aTempArray;
    if(appDelegate.isFullversionDownloading)
    {
        //insert into FileDownloadList ('FileURL','IsDownloaded','isSkipFile') values ('%@',%d,'0')
        aTempStr = @"select FileURL from FileDownloadList where isSkipFile = '1'";
        aTempArray = [[Database shareDatabase] getAllDataForQuery:aTempStr];
    }
    else {
        
        //aTempStr = @"select * from FileUpdateList where isSkipFile = '1'";
        aTempStr = @"select * from FileUpdateList where isSkipFile = '1' group by FileName";
        aTempArray = [[Database shareDatabase] getAllDataForQuery:aTempStr];
    }
    if (aTempArray.count == 0)
    {
        btnSkip.enabled = NO;
        [btnSkip setTitleColor:defineGrayColor forState:UIControlStateNormal];
    }
    else
    {
        btnSkip.enabled = YES;
        [btnSkip setTitleColor:defineBlackColor forState:UIControlStateNormal];
        //btnSkip.titleLabel.textColor = defineBlackColor;
    }
    if(isRegistered) {
        
        // CR Changes
        //  btnUninstall.enabled=YES;
        [BtnRegistration setTitle:@"Login" forState:UIControlStateNormal];//@"Unregister"
        
    } else {
        // CR Changes
        //   btnUninstall.enabled=NO;
        [BtnRegistration setTitle:@"Login" forState:UIControlStateNormal];//@"Register"
    }
    
    int aIntRegistered = [UserDefaults boolForKey:@"Registered"];
    if(aIntRegistered)
    {
        int aIntUpdate = [UserDefaults boolForKey:@"UpdateDownloaded"];
        
        if(aIntUpdate) {
            
            if(intupdate>0) {
                
                imgUpdatesAvailable.hidden = NO;
                imgMenuUpdates.hidden = NO;
                lblUpdatesavailable.hidden  = NO;
                lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",intupdate];
                lblMenuUpdates.hidden = NO;
                lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
                
            } else {
                imgMenuUpdates.hidden = YES;
                lblUpdatesavailable.hidden = YES;
                lblMenuUpdates.hidden = YES;
                imgUpdatesAvailable.hidden = YES;
            }
            
        } else {
            if(intupdate>0) {
                imgUpdatesAvailable.hidden = NO;
                imgMenuUpdates.hidden = NO;
                lblUpdatesavailable.hidden  = NO;
                lblUpdatesavailable.text = [NSString stringWithFormat:@"%d",intupdate];
                lblMenuUpdates.hidden = NO;
                lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
            }
            else {
                imgMenuUpdates.hidden = YES;
                lblUpdatesavailable.hidden = YES;
                lblMenuUpdates.hidden = YES;
                imgUpdatesAvailable.hidden = YES;
            }
        }
    }
    else
    {
        if(intupdate>0)
        {
            imgUpdatesAvailable.hidden = NO;
            lblUpdatesavailable.hidden  = NO;
            lblUpdatesavailable.text =[NSString stringWithFormat:@"%d",intupdate];
            lblMenuUpdates.hidden = NO;
            lblMenuUpdates.text = [NSString stringWithFormat:@"%d",intupdate];
        }
    }
    [self SetPlayerToNormalStateWithPause];
    [self setHelpButtonNormal];
    [btnMenu setImage:[UIImage imageNamed:@"Menu.png"] forState:UIControlStateNormal];
    [btnMenu setImage:[UIImage imageNamed:@"MenuPressed.png"] forState:UIControlStateSelected];
    
    if(!btnMenu.selected) {
        btnMenu.selected=YES;
        [self.view addSubview:viewPopup];
        btnInfo.selected=NO;
        
    }
    else
    {
        btnMenu.selected=NO;
        [viewPopup removeFromSuperview];
    }
    //  NSString  *aTempFileListStr = @"select FileURL from FileDownloadList";
    //  NSArray  *aTempFileListArray = [[Database shareDatabase] getAllDataForQuery:aTempFileListStr];
    
    NSString *aStrSql = [NSString stringWithFormat:@"select * from UpdatesList where UpdateDownloaded= \'%@\'",@"1"];
    BOOL aCheckDataDownloaded =  [[Database shareDatabase] CheckForRecord:aStrSql];
    if(aCheckDataDownloaded && isRegistered){
        btnUninstall.enabled = YES;
        [btnUninstall setTitleColor:defineBlackColor forState:UIControlStateNormal];
    }else{
        btnUninstall.enabled = NO;
        btnUninstall.titleLabel.textColor = defineGrayColor;
        [btnUninstall setTitleColor:defineGrayColor forState:UIControlStateNormal];
    }
}
#pragma mark Textfield and textView delegate methods
/*
 Previous : Undo/redo bar visible after 8.4 iOS version.
 New : Bar removed due to overlapping issue mentioned by client on basecamp at 23-1-2016.
 so added new method : textFieldDidBeginEditing.
 */
- (void)textFieldDidBeginEditing:(UITextField*)textField
{
    if(SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(@"9.0"))
    {
        UITextInputAssistantItem* item = textField.inputAssistantItem;
        item.leadingBarButtonGroups = @[];
        item.trailingBarButtonGroups = @[];
    }
}
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField
{
    return YES;
}
-(BOOL)textFieldShouldClear:(UITextField *)textField
{
    return YES;
}
-(BOOL)textFieldShouldReturn:(UITextField *)textField
{
    if((textField.returnKeyType = UIReturnKeyNext))
    {
        [txtFieldSerialnumber1 becomeFirstResponder];
        
        if(txtFieldSerialnumber1.text.length==16)
        {
            textField.returnKeyType = UIReturnKeyDone;
            [self RegistrationMethodCalled];
        }
    }
    
    return YES;
}
-(BOOL)textViewShouldBeginEditing:(UITextView *)textView
{
    return YES;
}
-(BOOL)textViewShouldEndEditing:(UITextView *)textView
{
    return YES;
}
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string1
{
    //NSString *str=[NSString stringWithFormat:@"%@%@",textField.text,string1];
    if(textField.tag!=0)
    {
        NSCharacterSet *character = [NSCharacterSet characterSetWithCharactersInString:NUMBERS];
        NSCharacterSet *inStringSet = [NSCharacterSet characterSetWithCharactersInString:string1];
        return [character isSupersetOfSet:inStringSet];
        //        NSUInteger newLength = [textField.text length] + [string1 length] - range.length;
        //        return (newLength > 16) ? NO : YES;
    }
    return YES;
    
}
- (IBAction)btnRegisterAction:(id)sender {
    [self NoInternetConnectionShowAlert];
    if (!isRegistered) {
        if(self.txtFieldEmail.text.length==0) {
            // Tag:1
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:defineEnterEmailAddress cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                [self dismissViewControllerAnimated:YES completion:nil];
                self.txtFieldEmail.text=@"";
            }];
        } else if(![self EmailValidation:self.txtFieldEmail.text]) {
            // Tag:2
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:defineEnterValidEmailAddress cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                [self dismissViewControllerAnimated:YES completion:nil];
                self.txtFieldEmail.text=@"";
            }];
            return;
        }
        else if(txtFieldSerialnumber1.text.length==0) {
            // tag : 0
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:defineEnterSericalNumber cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
        else if(txtFieldSerialnumber1.text.length>0) {
            if(AppDelegateobj.connected) {
                [self callRegisterwebService];
            } else {
                // tag : 0
                [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:ErrorMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            }
        }
    }
}
- (void)RegistrationMethodCalled {
    
    if (isRegistered) {
        
        if(AppDelegateobj.connected)
        {
            [self callRegisterwebService];
        }
        else
        {
            // tag : 0
            [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:ErrorMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        }
    }
    else
    {
        if(self.txtFieldEmail.text.length==0)
        {
            // Tag:1
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:defineEnterEmailAddress cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                [self dismissViewControllerAnimated:YES completion:nil];
                self.txtFieldEmail.text=@"";
            }];
        }
        
        else if(self.txtFieldEmail.text.length>0)
        {
            BOOL Validate = [self EmailValidation:self.txtFieldEmail.text];
            if(!Validate)
            {
                // Tag:2
                [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:defineEnterValidEmailAddress cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                    [self dismissViewControllerAnimated:YES completion:nil];
                    self.txtFieldEmail.text=@"";
                }];
                return ;
            }
            else
            {
                if(AppDelegateobj.connected)
                {
                    [self callRegisterwebService];
                }
                else
                {
                    // tag : 0
                    [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:ErrorMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                }
            }
        }
        
        else if(txtFieldSerialnumber1.text.length==0)
        {
            // tag : 0
            [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:defineEnterSericalNumber cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            
        }
        else if(txtFieldSerialnumber1.text.length>0)
        {
            if(AppDelegateobj.connected)
            {
                [self callRegisterwebService];
            }
            else
            {
                // tag : 0
                [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:ErrorMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            }
        }
    }
}
- (BOOL)EmailValidation:(NSString *) testEmail {
    NSString *emailRegex = @"[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}";
    NSPredicate *emailTest = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", emailRegex];
    return [emailTest evaluateWithObject:testEmail];
}

#pragma mark -
#pragma mark CancelRegistration

- (IBAction)CancelRegistrationView:(id)sender {
    
    [UserDefaults setObject:self.strEmail forKey:@"Email"];
    [UserDefaults setObject:self.strSerialNumber forKey:@"SerialNumber"];
    [UserDefaults synchronize];
    self.txtFieldEmail.text = self.strEmail;
    
    if(viewRegistration)
        [viewRegistration removeFromSuperview];
}

- (IBAction)btnAnimationclick:(id)sender {
    //    [self awakeFromNib];
    
    if (appDelegate.isPresentationInfoOpen) {
        [self btnCloseInfoClick:nil];
    }
    [self SetPlayerToNormalStateWithPause];
    
    //    btnInfo.selected=FALSE;
    //    m_playerView.frame = CGRectMake(0, 0, 929, 696);
    //    m_playerView.contentMode=UIViewContentModeScaleToFill;
    
    if ([sender isSelected]) {
        
        // This is done for client comment "Presentation Info should not close on click of Animation button" going from FullScreenMode to SelectionPanel(For only Item selected)...
        appDelegate.isReOpenPresentationInfo = NO;
        
        if ([UserDefaults boolForKey:@"isLocked"]) {
            
            [animation btnLockClicked];
        }
        
        if (view_Back) {
            [view_Back removeFromSuperview];
        }
        
        //[animation tapVideoFromPlaylist:sender];
        if (appDelegate.intSelectedTab == 1) {
            [btnInfo setEnabled:NO];
        }
        else
        {
            [btnInfo setEnabled:YES];
        }
        
        
        lblFramcounter.hidden=NO;
        imgFramecounter.hidden=NO;
        playPauseBtn.hidden=NO;
        loopBtn.hidden=NO;
        sliderSpeedControl.hidden=NO;
        btn_SpeedControl.alpha = 1.0f;
        
        if(animation) {
            
            [animation.view removeFromSuperview];
            
            if(intFullversiondownloadCompleted||intUnregisterDone) {
                
                animation = nil;
                animation.videoPlayDelegate = nil;
                
            }
            
            if(appDelegate.isUpdateDownloading) {
                animation = nil;
                animation.videoPlayDelegate = nil;
            }
        }
        
        intUnregisterDone=0;
        intFullversiondownloadCompleted=0;
        AppDelegateobj.isUpdateDownloading = FALSE;
        
        [sender setSelected:NO];
        
        [btnHelp setHidden:NO];
        [btnPreviousFrame setHidden:NO];
        [btnNextFrame setHidden:NO];
        [btnInfo setHidden:NO];
        [btnMenu setHidden:NO];
        
        // Enabling
        sliderTimeForVideo.enabled = YES;
        sliderSpeedControl.enabled = YES;
        loopBtn.enabled = YES;
        playPauseBtn.enabled = YES;
        //        [btnPreviousFrame setEnabled:YES];
        //        [btnNextFrame setEnabled:YES];
        [btnMenu setEnabled:YES];
        
        if ([[NSUserDefaults standardUserDefaults]boolForKey:@"PlayAnimation"])
        {
            if (!appDelegate.isFavourite) {
                if (appDelegate.intPrevSelectedTab == 0 && appDelegate.selectedObj == nil) {
                    appDelegate.arrScrollViewCount = appDelegate.arrMovie;
                } else if (appDelegate.intPrevSelectedTab == 1) {
                    appDelegate.arrScrollViewCount = appDelegate.arrImage;
                } else {
                    appDelegate.arrScrollViewCount = appDelegate.arrPresentation;
                }
            }
        }
        else {
            appDelegate.arrScrollViewCount = appDelegate.arrUserDefineSeq;
        }
        
        // 2.4 changes
        //        if ([[NSUserDefaults standardUserDefaults] boolForKey:@"PlayAnimation"]) {
        
        if (appDelegate.intSelectedScrNo > 0) {
            appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[appDelegate.intSelectedScrNo-1] ;//after adding or removing update playlist.
        }
        else
        {
            appDelegate.mutArrPlayVideo = (appDelegate.arrScrollViewCount)[0] ;//after adding or removing update playlist.
        }
        [self removeAddButton];
        //        }
        
        //Rohan Condition
        NSInteger total = (AppDelegateobj.mutArrPlayVideo).count;
        NSInteger aintSelectedVideoIndex = AppDelegateobj.intSelectedVideo;
        intcountofVideo=currentIndex=aintSelectedVideoIndex;
        
        if (total == 1) {
            [btnNextFrame setEnabled:NO];
            [btnPreviousFrame setEnabled:NO];
        }else if (aintSelectedVideoIndex == total-1) {
            [btnNextFrame setEnabled:NO];
            [btnPreviousFrame setEnabled:YES];
        }else{
            if (aintSelectedVideoIndex == 0) {
                [btnNextFrame setEnabled:YES];
                [btnPreviousFrame setEnabled:NO];
            }else{
                [btnNextFrame setEnabled:YES];
                [btnPreviousFrame setEnabled:YES];
            }
        }
        [self manageBtnGlobleShowHideImg];
        [self mangePhotoVideoFlow:AppDelegateobj.intSelectedVideo];
        
        if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
            
            if (AppDelegateobj.isUpdateDownloading) {
                
                if (isUpdateInterrupted == 1) {
                    
                    btnRemainDownload.hidden = NO;
                } else {
                    btnRemainDownload.hidden = YES;
                }
            } else {
                
                
                NSMutableArray *arrAllFile = [NSMutableArray array] ;
                NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
                [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
                
                if (arrAllFile.count > 0) {
                    
                    btnRemainDownload.hidden = NO;
                    
                } else {
                    btnRemainDownload.hidden = YES;
                }
            }
            
        } else {
            
            btnRemainDownload.hidden = YES;
        }
        
    }
    else {
        
        
        if(intFullversiondownloadCompleted||intUnregisterDone) {
            
            animation = nil;
            animation.videoPlayDelegate = nil;
            
        }
        
        if(AppDelegateobj.isUpdateDownloading) {
            
            animation = nil;
            animation.videoPlayDelegate = nil;
        }
        
        //        if(animation==nil) {
        animation = [[AnimationPanelVC alloc]initWithNibName:@"AnimationPanelVC" bundle:nil];
        animation.videoPlayDelegate = self;
        //        }
        sliderSpeedControl.enabled = NO;
        loopBtn.enabled = NO;
        playPauseBtn.enabled = NO;
        
        //Create Animation Pane - Scrollview
        animation.view.frame = CGRectMake(0, 0, 929, 748);
        (animation.view).backgroundColor = [UIColor darkTextColor];
        verticalToolbarView.frame  = CGRectMake(929, 0, 95, 696);
        [self.view addSubview:animation.view];
        
        /// New change Version 2.5...
        //        [self addChildViewController:animation];
        //        [animation didMoveToParentViewController:self];
        
        [sender setSelected:YES];
        [btnInfo setEnabled:NO];
        [btnPreviousFrame setEnabled:NO];
        [btnNextFrame setEnabled:NO];
        [btnMenu setEnabled:YES];
        btnRemainDownload.hidden = YES;
        btn_SpeedControl.alpha = 0.5f;
        
    }
    
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:YES];
    // [self.m_queueplayer removeAllItems];
    sliderTimeForVideo=nil;
    [btnHideUnhideImages removeFromSuperview];
}

- (void)dealloc {
    // Remove observer when view controller is deallocated
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
/*
 #pragma mark -
 #pragma  mark alertView delegate methods
 
 - (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
 if(alertView.tag==1||(alertView.tag==2))
 {
 
 }
 else if(alertView.tag==5)
 {
 
 }
 else if(alertView.tag==6)
 {
 // not used
 //        [self startXMLParsing];
 //        if(viewDownloadingData)
 //        {
 //            [viewDownloadingData removeFromSuperview];
 //        }
 }
 else if(alertView.tag==12)
 {
 
 
 }
 else if(alertView.tag==99)
 {
 
 }
 else if (alertView.tag == 232)
 {
 
 }
 else if (alertView.tag == 233)
 {
 
 }
 else if (alertView.tag == REMAIN_DOWNLOAD) {
 
 }
 else if(alertView.tag==999)
 {
 // not used
 //        progressBar.hidden = YES;
 //        lblProgresspercent.hidden = YES;
 //        [btnMenu setEnabled:YES];
 }
 else
 {
 [UserDefaults setBool:NO forKey:@"Deactivation"];
 [UserDefaults synchronize];
 }
 }
 */
- (void)deleteOldData {
    
    // Remove data from Document Directory...
    // This is for version 2.3 to 2.4 upgrade changes
    [self removeOldVersionDataFromDocumentDirectory];
    
    [SharedDatabase Delete:@"DELETE FROM UpdateMaster"];
    [SharedDatabase Delete:@"DELETE FROM UpdatesList"];
    //    [SharedDatabase Delete:@"DELETE FROM FileDownloadList"];
    [SharedDatabase Delete:@"DELETE FROM FileUpdateList"];
    [SharedDatabase Delete:@"DELETE FROM UpdateFileMaster"];
    
    // To preserve User define sequences first set FileTitle, so we can get back data using FileTitle. We need to use FileTitle because fileID will change after deleting data..
    [self setFileTitleInUserDefineSequences];
    
    // Set selection index...
    appDelegate.intSelectedScrNo = 0;
    appDelegate.intSelectedVideo  = 0;
    
#pragma mark --------------------------------
#pragma mark Version 2.4 Changes
    // Don't delete UserDefined photos...
    
    NSString *aStrQuery = [NSString stringWithFormat:@"select collectionID from CollectionMaster where isUserDefine = \'1\'"];
    NSMutableArray *aMutArrCollectionIds = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
    
    NSString *aStrDeleteQuery = @"DELETE FROM CollectionFilesMaster";
    NSString *aStrDeleteFilesQuery = @"DELETE FROM LocalFileMaster";
    
    NSString *aStrCollectionIds = @"";
    
    for (int i = 0; i < aMutArrCollectionIds.count; i++) {
        if (i == 0)
        {
            aStrCollectionIds = [aMutArrCollectionIds[i] valueForKey:@"collectionID"];
        }
        else
        {
            aStrCollectionIds = [NSString stringWithFormat:@"%@,%@", aStrCollectionIds, [aMutArrCollectionIds[i] valueForKey:@"collectionID"]];
        }
        
    }
    if(aMutArrCollectionIds.count > 0) {
        
        aStrQuery = [NSString stringWithFormat:@"select filesID from CollectionFilesMaster where collectionID IN (%@)", aStrCollectionIds];
        
        NSMutableArray *aMutArrFileIds = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
        
        NSString *aStrFileIds = @"";
        
        for (int i = 0; i < aMutArrFileIds.count; i++) {
            if (i == 0)
            {
                aStrFileIds = [aMutArrFileIds[i] valueForKey:@"filesID"];
            }
            else
            {
                aStrFileIds = [NSString stringWithFormat:@"%@,%@", aStrFileIds, [aMutArrFileIds[i] valueForKey:@"filesID"]];
            }
            
        }
        
        aStrDeleteFilesQuery = [NSString stringWithFormat:@"DELETE FROM LocalFileMaster where fileID NOT IN (%@)", aStrFileIds];
        aStrDeleteQuery = [NSString stringWithFormat:@"DELETE FROM CollectionFilesMaster where collectionID NOT IN (%@)", aStrCollectionIds];
    }
    
    // Delete data from LocalFileMaster and CollectionFilesMaster...
    [SharedDatabase Delete:aStrDeleteFilesQuery];
    [SharedDatabase Delete:aStrDeleteQuery];
    [SharedDatabase Delete:@"DELETE FROM CollectionMaster where isUserDefine = \'0\'"];
    
}

//Start  Unintall Updates - User Define sequence
- (void)deleteUserDefineSequenceForUninstallUpdate :(NSString*)aFileId{
    if ([self checkFileIdFromUD:aFileId]) {
        NSString *strGetCollId = [NSString stringWithFormat:@"select collectionID from CollectionUserDefineFiles where filesID= \'%@\'",aFileId];
        NSMutableArray *aMutArrCollId = [[Database shareDatabase] getAllDataForQuery:strGetCollId];
        
        
        NSString *aTempDeleted = [NSString stringWithFormat:@"DELETE FROM CollectionUserDefineFiles where filesID= \'%@\'",aFileId];
        [SharedDatabase Delete:aTempDeleted];
        aTempDeleted = nil;
        
        for (NSDictionary *aDic in aMutArrCollId) {
            if (![self checkCollectionIdUD:aDic[@"collectionID"]]) {
                NSString *aTempDel = [NSString stringWithFormat:@"DELETE FROM CollectionUserDefine where collectionID= \'%@\'",aDic[@"collectionID"]];
                [SharedDatabase Delete:aTempDel];
                aTempDel = nil;
            }
            
        }
        
        [aMutArrCollId removeAllObjects];
    }
}

-(BOOL)checkFileIdFromUD:(NSString*)aFileId{
    NSString *aStrSql = [NSString stringWithFormat:@"select filesID from CollectionUserDefineFiles where filesID= \'%@\'",aFileId];
    
    return [[Database shareDatabase] CheckForRecord:aStrSql];
}


-(BOOL)checkCollectionIdUD:(NSString*)aCollId{
    
    NSString *aStrSql = [NSString stringWithFormat:@"select * from CollectionUserDefineFiles where collectionID= \'%@\'",aCollId];
    
    return [[Database shareDatabase] CheckForRecord:aStrSql];
}

//End  Unintall Updates - User Define sequence

/*
 - (void)methodUnzipping {
 
 if(FullorUpdate==1)
 {
 isFullVersionDownloaded = TRUE;
 isUpdateDownloaded = FALSE;
 [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
 [btnMenu setEnabled:YES];
 [UserDefaults setBool:YES forKey:@"FullDownloaded"];
 [UserDefaults synchronize];
 //        [self unzipfile];
 lblProgresspercent.text=@"";
 lblProgresspercent.text = @"Full-Version Download Completed";
 progressBar.hidden = YES;
 [UserDefaults setBool:NO forKey:@"FullDownloaded"];
 [UserDefaults synchronize];
 [self startXMLParsing];
 [activityIndiForDownload stopAnimating];
 [activityIndiForDownload setHidden:YES];
 [UserDefaults setBool:YES forKey:@"FullDownloaded"];
 [UserDefaults synchronize];
 
 [UserDefaults  setBool:NO forKey:@"isDowmloadCancelled"];
 [UserDefaults  synchronize];
 
 }
 else
 {
 
 isFullVersionDownloaded = FALSE;
 [UserDefaults setBool:NO forKey:@"FullDownloaded"];
 [UserDefaults synchronize];
 }
 
 }
 */

#pragma mark---
#pragma mark RemoveFromDocumentDirectory

- (void)RemoveFromDocumentDirectory {
    
    // Delete the file using NSFileManager
    NSString *aStrUpdatePath = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"/Update0001"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL successUpdate = [fileManager fileExistsAtPath:aStrUpdatePath];
    
    if(successUpdate)
    {
        [fileManager removeItemAtPath:aStrUpdatePath error:nil];
    }
    
    
    
    NSString *aStrUpdatePath1 = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"/BiteFXiPadFull"];
    NSFileManager *fileManager1 = [NSFileManager defaultManager];
    BOOL successUpdate1 = [fileManager1 fileExistsAtPath:aStrUpdatePath1];
    
    if(successUpdate1)
    {
        [fileManager1 removeItemAtPath:aStrUpdatePath1 error:nil];
    }
    
#pragma mark --------------------------------
#pragma mark Version 2.4 Folder Structure Changes
    
    aStrUpdatePath1 = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:BITEFXV2];
    successUpdate1 = [fileManager1 fileExistsAtPath:aStrUpdatePath1];
    
    if(successUpdate1)
    {
        [fileManager1 removeItemAtPath:aStrUpdatePath1 error:nil];
    }
    
    // Version 3.0 changes
    [AppDelegate createFoldersInDocumentDirectoryToSaveAppData];
    [appDelegate copyFile];
    [appDelegate copyUpdateFile];
    
}

#pragma mark Touchview delegate methods
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    
    // Disallow recognition of tap gestures in the segmented control.
    if (touch.view == viewUpdates||touch.view==viewRegistration||touch.view==viewAboutBitefx) {
        return NO;
        
    } else {
        
        return YES;
    }
}

- (void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    [self setMenuButtonNormal];
    [self setHelpButtonNormal];
    
    UITouch *touch  = [event.allTouches anyObject];
    if (touch.view==viewAboutBitefx||touch.view==viewPopup || touch.view == viewPurchase) {
        
        if(viewAboutBitefx.superview)
            [viewAboutBitefx removeFromSuperview];
        if(viewPopup.superview)
            [viewPopup removeFromSuperview];
        if([self.view.subviews containsObject:wbView])
        {
            btnInfo.selected=YES;
        }
        else
        {
            btnInfo.selected=NO;
        }
        if (viewPurchase.superview)
            [viewPurchase removeFromSuperview];
    }
    if(touch.view == sliderTimeForVideo) {
        isPlaying = FALSE;
    }
}
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    btnHelp.selected = NO;
}
/*
 - (void)viewDidUnload {
 
 lblUpdatesAvailble = nil;
 btnSkip = nil;
 tblViewSkipUpdate = nil;
 viewSkipList = nil;
 [super viewDidUnload];
 [[NSNotificationCenter defaultCenter] removeObserver:self name:@"status" object:nil];
 
 // Release any retained subviews of the main view.
 // e.g. self.myOutlet = nil;
 }
 
 - (BOOL)shouldAutorotateToInterfaceOrientation:(UIInterfaceOrientation)interfaceOrientation {
 
 return &UIDeviceOrientationIsLandscape;
 
 }
 */
#pragma mark - Database Methods

- (void)getDataAboutUserFromDatabase
{
    NSString *aStrGetData = [NSString stringWithFormat:@"select emailId, isRegistered, isSubscribed, SerialNumber from UserMaster where userId = '1'"];
    
    NSMutableArray *mutArrData = [SharedDatabase getAllDataForQuery:aStrGetData];
    
    
    NSMutableDictionary *mutDictData = mutArrData[0];
    
    self.strEmail = [NSString stringWithFormat:@"%@",mutDictData[@"emailId"]];
    
    self.strSerialNumber = mutDictData[@"SerialNumber"];
    
    if ([mutDictData[@"isRegistered"] isEqualToString:@"0"])
    {
        isRegistered = FALSE;
    }
    else
    {
        isRegistered = TRUE;
    }
    
    if ([mutDictData[@"isSubscribed"] isEqualToString:@"0"])
    {
        isSubscribed = FALSE;
    }
    else
    {
        isSubscribed = TRUE;
    }
}

- (void)insertNewFileWithList:(NSMutableArray *)aMutArrFiles
{
    if (aMutArrFiles.count > 0)
    {
        for (int intCntI = 0; intCntI  < aMutArrFiles.count; intCntI++)
        {
            NSString *aStrExist = [NSString stringWithFormat:@"select count(fileTitle) from LocalFileMaster where fileTitle = \'%@\'",aMutArrFiles[intCntI][@"file"]];
            
            NSMutableArray *aMutArrExist = [SharedDatabase getAllDataForQuery:aStrExist];
            
            if ([aMutArrExist[0][@"count(fileTitle)"] intValue] == 0)
            {
                
                //                NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName', 'fileShortDispName', 'isDownloaded', 'isPlayed', 'filePath', 'infoFilePath','thumbnailPath') values (\'%@.mov\', \'%@\', \'%@\', '0', '1', \'%@\', \'%@\', \'%@\')",aStrNm,aStrDN,aStrSN,aStrURLForVideo,aStrURLForHtml,aStrURLForJpg];
                //                [SharedDatabase Insert:aStrInsertQuery];
                
                NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into LocalFileMaster ('fileTitle', 'fileName') values (\'%@\', \'%@\')",aMutArrFiles[intCntI][@"file"],aMutArrFiles[intCntI][@"name"]];
                [SharedDatabase Insert:aStrInsertQuery];
            }
            
        }
    }
}

#pragma mark Insert Update count

- (void)insertUpdateCount:(int)aIntCount
{
    
    NSDateFormatter *aDtFrmt = [[NSDateFormatter alloc] init];
    aDtFrmt.dateFormat = @"dd/MM/yyyy";
    NSString *strDate = [aDtFrmt stringFromDate:[NSDate date]];
    
    
    NSString *aStrInsertCount = [NSString stringWithFormat:@"update UpdateMaster set availableUpdateCount = \'%d\' , LastUpdateCheckDate = \'%@\' where UpdateMasterUniqueID = '1'",aIntCount,strDate];
    
    //    [SharedDatabase Insert:aStrInsertCount];
    [[Database shareDatabase]Update:aStrInsertCount];
    
}

#pragma mark Insert Update List

- (void)insertUpdateList:(NSMutableDictionary *)aMutDictList
{
    NSString *aStrListExist = [NSString stringWithFormat:@"select count(UpdateVersion) from UpdatesList where UpdateVersion = \'%d\'",[aMutDictList[@"version_number"] intValue]];
    
    NSMutableArray *aMutArrExists = [SharedDatabase getAllDataForQuery:aStrListExist];
    
    if ([aMutArrExists[0][@"count(UpdateVersion)"] intValue] == 0) // checking for already exist Version Entry
    {
        NSString *aStrInsertList = [NSString stringWithFormat:@"insert into UpdatesList ('updateDate', 'UpdateVersion', 'UpdateDescription', 'xmlfilePath') values (\'%@\', \'%d\', \"%@\", \'%@\')",aMutDictList[@"update_date"],[aMutDictList[@"version_number"] intValue],aMutDictList[@"description"],aMutDictList[@"placement_determination_file"]];
        
        [SharedDatabase Insert:aStrInsertList];
        
        // Inserting File List into Update Files
        // Old code...
        //        NSMutableArray *aMutArrFilesList = [aMutDictList objectForKey:@"files"];
        //
        //        if ([aMutArrFilesList count] > 0)
        //        {
        //            for (int aIntCntI = 0; aIntCntI < [aMutArrFilesList count]; aIntCntI++)
        //            {
        //                [self insertUpdateFiles:[aMutArrFilesList objectAtIndex:aIntCntI] forVersion:[[aMutDictList objectForKey:@"version_number"] intValue]];
        //            }
        //
        //        }
        
        //===================
        // Version 2.4 Changes
        //===================
        // updatesList3 response is changed, so now we get dictionary instead of array for "files" key...
        NSDictionary *aDictFilesData = aMutDictList[@"files"];
        [self insertIntoUpdateMaster:aDictFilesData andVersionNumber:aMutDictList[@"version_number"]];
    }
}

- (void)insertIntoUpdateMaster:(NSDictionary *)dictFilesData andVersionNumber:(NSString *)strVersion {
    
    //-----------------
    //----Movie--------
    if (dictFilesData[@"movie"]) {
        
        NSArray *aArrMovies = dictFilesData[@"movie"];
        [self insertUpdateListInDatabase:aArrMovies andVersionNumber:strVersion];
    }
    
    //-----------------
    //----MovieInfo--------
    if (dictFilesData[@"movieinfo"]) {
        
        NSArray *aArrMoviesInfo = dictFilesData[@"movieinfo"];
        [self insertUpdateListInDatabase:aArrMoviesInfo andVersionNumber:strVersion];
    }
    
    //-----------------
    //----MovieThumb--------
    if (dictFilesData[@"moviethumb"]) {
        
        NSArray *aArrMoviesThumb = dictFilesData[@"moviethumb"];
        [self insertUpdateListInDatabase:aArrMoviesThumb andVersionNumber:strVersion];
    }
    
    //-----------------
    //----Image--------
    if (dictFilesData[@"image"]) {
        
        NSArray *aArrImages = dictFilesData[@"image"];
        [self insertUpdateListInDatabase:aArrImages andVersionNumber:strVersion];
    }
    
    //-----------------
    //----ImageThumb--------
    if (dictFilesData[@"imagethumb"]) {
        
        NSArray *aArrImagesThumb = dictFilesData[@"imagethumb"];
        [self insertUpdateListInDatabase:aArrImagesThumb andVersionNumber:strVersion];
    }
}

- (void)insertUpdateListInDatabase:(NSArray *)arrData andVersionNumber:(NSString *)strVersion {
    
    for (int i=0; i < arrData.count; i++) {
        
        // Insert file url...
        NSString *aStrFileUrl = arrData[i];
        
        // Remove extra backslash in file path...
        aStrFileUrl = [aStrFileUrl stringByReplacingOccurrencesOfString:@"\\" withString:@"/"];
        
        NSString *aStrFileTitle = aStrFileUrl.lastPathComponent;
        
        NSString *aStrFileName = aStrFileTitle.stringByDeletingPathExtension;
        
        NSString *aStrGetId = [NSString stringWithFormat:@"select UpdateUniqueId from UpdatesList where UpdateVersion = \'%d\'", strVersion.intValue];
        
        NSMutableArray *aMutArrVersion = [SharedDatabase getAllDataForQuery:aStrGetId];
        
        NSString *aStrInsertUpdateFile = [NSString stringWithFormat:@"insert into UpdateFileMaster ('updateUniqueId', 'fileTitle', 'filename') values (\'%@\', \'%@\', \'%@\')",aMutArrVersion[0][@"UpdateUniqueId"], aStrFileTitle, aStrFileName];
        
        [SharedDatabase Insert:aStrInsertUpdateFile];
        
    }
    
}

#pragma mark Insert Into update Files

- (void)insertUpdateFiles:(NSMutableDictionary *)aMutDictFiles forVersion:(int)aIntVers {
    
    NSString *aStrGetId = [NSString stringWithFormat:@"select UpdateUniqueId from UpdatesList where UpdateVersion = \'%d\'",aIntVers];
    
    NSMutableArray *aMutArrVersion = [SharedDatabase getAllDataForQuery:aStrGetId];
    
    NSString *aStrInsertUpdateFile = [NSString stringWithFormat:@"insert into UpdateFileMaster ('updateUniqueId', 'fileTitle', 'filename') values (\'%@\', \'%@\', \'%@\')",aMutArrVersion[0][@"UpdateUniqueId"], aMutDictFiles[@"file"], aMutDictFiles[@"name"]];
    
    [SharedDatabase Insert:aStrInsertUpdateFile];
    
}

#pragma mark Get Update List
- (NSMutableArray *)getUpdateList
{
    NSString *aStrGetlist = [NSString stringWithFormat:@"select UpdateVersion, UpdateDescription,UpdateDate,xmlfilePath from UpdatesList where UpdateDownloaded = '0' ORDER BY UpdateVersion ASC;"];
    
    NSMutableArray *aMutArrResult = [SharedDatabase getAllDataForQuery:aStrGetlist];
    return aMutArrResult;
}


#pragma mark Download List
- (void)getDownloadUpdateList
{
    NSString *aStrRequest = [NSString stringWithFormat:@"select fileTitle, updateUniqueId from UpdateFileMaster"];
    
    NSMutableArray *aMutArrResult = [SharedDatabase getAllDataForQuery:aStrRequest];
    
    // get version And Add into New Array
    
    if (mutArrUpdateDownload)
    {
        [mutArrUpdateDownload removeAllObjects];
    }
    
    for (int aIntCnt = 0; aIntCnt < aMutArrResult.count; aIntCnt ++)
    {
        NSMutableDictionary *aMutDict = [[NSMutableDictionary alloc] init];
        
        NSString *aStrVersionReq = [NSString stringWithFormat:@"select UpdateVersion,xmlfilePath from UpdatesList where UpdateUniqueId = \'%d\'",[aMutArrResult[aIntCnt][@"updateUniqueId"] intValue]];
        
        NSMutableArray *aMutArrVer = [SharedDatabase getAllDataForQuery:aStrVersionReq];
        
        aMutDict[@"UpdateVersion"] = aMutArrVer[0][@"UpdateVersion"];
        
        aMutDict[@"fileTitle"] = aMutArrResult[aIntCnt][@"fileTitle"];
        
        aMutDict[@"xmlfilePath"] = aMutArrVer[0][@"xmlfilePath"];
        
        [mutArrUpdateDownload addObject:aMutDict];
        
    }
}


#pragma mark Get Maximum Version

- (int)getVersionToCheckUpdate
{
    int aIntResult = -99;
    
    NSString *aStrGetlist = [NSString stringWithFormat:@"select UpdateVersion from UpdatesList where UpdateDownloaded='1'"];
    
    NSMutableArray *aMutArrResult = [SharedDatabase getAllDataForQuery:aStrGetlist];
    
    if (aMutArrResult.count > 0)
    {
        for (int aIntCntI = 0; aIntCntI < aMutArrResult.count; aIntCntI++)
        {
            int aIntTemp = [aMutArrResult[aIntCntI][@"UpdateVersion"] intValue];
            
            if (aIntResult < aIntTemp)
            {
                aIntResult = aIntTemp;
            }
        }
    }
    
    return aIntResult;
    
}

#pragma mark CheckInterval Update

- (void)checkForIntervalUpdate
{
    NSString *aStrRequest = [NSString stringWithFormat:@"select lastUpdateCheckDate from UpdateMaster where UpdateMasterUniqueID = '1'"];
    
    NSMutableArray *aMutArrLastUpdatedDate = [SharedDatabase getAllDataForQuery:aStrRequest];
    
    if(aMutArrLastUpdatedDate.count>0)
    {
        NSString *aStrDt = aMutArrLastUpdatedDate[0][@"lastUpdateCheckDate"];
        
        NSDateFormatter *aDtFrmt = [[NSDateFormatter alloc] init];
        
        // this is imporant - we set our input date format to match our input string
        // if format doesn't match you'll get nil from your string, so be careful
        
        aDtFrmt.dateFormat = @"dd/MM/yyyy";
        
        //NSDate *aDtLastUpdatedDate = [[NSDate alloc] init];
        // voila!
        NSDate *aDtLastUpdatedDate = [aDtFrmt dateFromString:aStrDt];
        NSDate *aDtCurrent = [NSDate date];
        
        NSCalendar *calendar = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
        
        NSDateComponents *components = [calendar components:NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay
                                                   fromDate:aDtLastUpdatedDate
                                                     toDate:aDtCurrent
                                                    options:0];
        
        if (components.day >= 7)
        {
            
            if(AppDelegateobj.connected)
            {
                if (userSubStat == Subscribed)
                {
                    [self callUpdateCheckWebservice:nil];
                }
                else if (userSubStat == InAppSubscribed)
                {
                    [self callUpdateCheckForInAppWebservice:nil];
                }
            }
            else
            {
                //            UIAlertView *alertMsg = [[UIAlertView alloc]initWithTitle:ErrorTitle message:ErrorMsg delegate:self cancelButtonTitle:strAlertCancelTitle otherButtonTitles: nil];
                //            [alertMsg show];
                //            [alertMsg release];
            }
        }
    }
}

#pragma mark - Save Video with Data



#pragma mark - XML Parsing method

- (void)startXMLParsing {
    
    AppDelegateobj.isUpdatedMVIfile = FALSE;
    NSData *xmldata;
    
#pragma mark --------------------------------
#pragma mark Version 2.4 Folder Structure Changes
    
    NSString *aStrDirPath = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:BITEFXV2];
    NSString *aStrMvi  = [NSString stringWithFormat:@"%@/BiteFXiPadFullInventory.mvi", aStrDirPath];
    
    //    NSString *dir_path= [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"BiteFXiPadFull/BiteFXiPadFullInventory.mvi"];
    
    //  NSString *dir_path = [NSString stringWithContentsOfURL:[NSURL URLWithString:@"http://ipad.bitefx.com/version/BiteFXiPadFullInventory.MVI"] usedEncoding:Nil error:Nil];
    //local parsing
    int aIntRegistered  = [UserDefaults boolForKey:@"Registered"];
    int Fulldownload    = [UserDefaults boolForKey:@"FullDownloaded"];
    
    if(aIntRegistered) {
        if(Fulldownload) {
            
            xmldata=[NSData dataWithContentsOfFile:aStrMvi];;
            if(!xmldata){
                xmldata=[NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"BiteFXiPadFullInventory" ofType:@"mvi"]];
            }
            
        } else {
            
            xmldata=[NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"BiteFXiPadBasicInventory" ofType:@"MVI"]];
        }
        
    } else {
        
        xmldata=[NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"BiteFXiPadBasicInventory" ofType:@"MVI"]];
    }
    
    NSString *myString;
    myString = [[NSString alloc] initWithData:xmldata encoding:NSASCIIStringEncoding];
    
    NSLog(@"Data to String ==== %@",myString);
    
    NSXMLParser *xmlParser = [[NSXMLParser alloc] initWithData:xmldata];
    
    //Initialize the delegate.
    // common
    XMLParser *parser = [[XMLParser alloc] initXMLParser];
    
    //Set delegate
    xmlParser.delegate = parser;
    
    //Start parsing the XML file.
    BOOL successParse = [xmlParser parse];
    if(successParse){
        //NSLog(@"No Errors");
        
        //        arrFileDownloadList = [AppDelegate listOfRemainFileDownload];
        //        if ([arrFileDownloadList count] > 0) {
        ////            [self startFullVersionFileDownload];
        //            [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(startFullVersionFileDownload) userInfo:nil repeats:NO];
        //        }
        
    } else {
        
        NSLog(@"startXMLParsing Error Error Error!!!");
    }
    
    //TODO: Bug Fix
    //    if(shouldStartDownload)
    //        [self startFullVersionFileDownload];
    
}


- (void)startXMLParsingUpdated {
    
    AppDelegateobj.isUpdatedMVIfile = TRUE;
    
    NSString *dir_path = @"";
    XMLParser *parser = [[XMLParser alloc] initXMLParser];
    
    for(int i=0;i<(self.arrMviFile).count;i++) {
        
        NSString *aStr = (self.arrMviFile)[i][@"mviFile"];
        
        NSString *aStrVersion = [NSString stringWithFormat:@"Update0001/%@",aStr];
        dir_path =  [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:aStrVersion];
        
        //xmldata=[NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"BiteFXiPadFullInventory" ofType:@"MVI"]];
        NSData* xmldata =  [NSData dataWithContentsOfFile:dir_path];
        
        
        //    NSString* newStr = [NSString stringWithUTF8String:[xmldata bytes]];
        NSXMLParser *xmlParser = [[NSXMLParser alloc] initWithData:xmldata];
        
        //Initialize the delegate.
        // common
        
        //Set delegate
        xmlParser.delegate = parser;
        
        //Start parsing the XML file.
        BOOL successParsing = [xmlParser parse];
        
        if(successParsing) {
            NSLog(@"No Errors");
        }
        else
            NSLog(@"startXMLParsingUpdated Error Error Error!!!");
    }
}

- (void)startXMLUpdatedParsing {
    
    [NSUserDefaults resetStandardUserDefaults];
    AppDelegateobj.isUpdatedMVIfile = TRUE;
    
    NSString *dir_path= [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"Update0001/BiteFXiPadUpdate00001.MVI"];
    
    //Memory Leaks Commments
    NSLog(@"%@",dir_path);
    
    // NSData* xmldata=[NSData dataWithContentsOfFile:dir_path];
    NSData* xmldata=[NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"BiteFXiPadUpdate00001" ofType:@"MVI"]];
    //    NSString* newStr = [NSString stringWithUTF8String:[xmldata bytes]];
    NSXMLParser *xmlParser = [[NSXMLParser alloc] initWithData:xmldata];
    
    //Initialize the delegate.
    // common
    XMLParser *parser = [[XMLParser alloc] initXMLParser];
    
    //Set delegate
    xmlParser.delegate = parser;
    
    //Start parsing the XML file.
    BOOL successParsing = [xmlParser parse];
    
    if(successParsing) {
        
        NSLog(@"No Errors");
    }
    else
        NSLog(@"startXMLUpdatedParsing Error Error Error!!!");
    
    
}

- (void)startXMLParsingFeatureSet {
    
    NSString *aStrFeaturesMviFile = [UserDefaults objectForKey:FEATURES_MVI_FILE_URL];
    NSString *aStrDirPath = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:BITEFXV2];
    NSString *aStrFilePath  = [NSString stringWithFormat:@"%@/%@", aStrDirPath, aStrFeaturesMviFile];
    
    //    NSData *xmldata = [NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"Features" ofType:@"MVI"]];
    NSData *xmldata = [NSData dataWithContentsOfFile:aStrFilePath];
    NSString *myString;
    myString = [[NSString alloc] initWithData:xmldata encoding:NSASCIIStringEncoding];
    
    NSLog(@"Data to String ==== %@",myString);
    
    NSXMLParser *xmlParser = [[NSXMLParser alloc] initWithData:xmldata];
    
    //Initialize the delegate.
    XMLParserFeature *parser = [[XMLParserFeature alloc] initXMLParserFeature];
    
    //Set delegate
    xmlParser.delegate = parser;
    
    //Start parsing the XML file.
    BOOL successParse = [xmlParser parse];
    if(successParse){
        NSLog(@"No Errors");
    } else {
        NSLog(@"startXMLParsing Feature Error Error Error!!!");
    }
    
}

#pragma mark Playlist Collection DB Method

- (void)insertPlayListCollection {
    //insertPlayListCollection
    //    for (int aIntC = 6; aIntC < 11; aIntC++) {
    //
    //        NSString *aStrCheckExistance = [NSString stringWithFormat:@"select count(collectionID) from CollectionMaster where collectionID = \'%d\'",aIntC];
    //
    //        NSMutableArray *aMutArrCheckExistance = [SharedDatabase getAllDataForQuery:aStrCheckExistance];
    //
    //        if ([[[aMutArrCheckExistance objectAtIndex:0] objectForKey:@"count(collectionID)"] intValue] == 0) {
    //
    //            NSString *aStrInsertQuery = [NSString stringWithFormat:@"insert into CollectionMaster ('collectionID', 'collectionName', 'collectionDisplayName') values (\'%d\', \'PlayList%d\', \'PlayList%d\')",aIntC, aIntC - 5, aIntC - 5];
    //
    //            [SharedDatabase Insert:aStrInsertQuery];
    //        }
    //    }
}

// Delete Playlist Record

- (void)deletePlaylistData {
    
    for (int aIntC = 7; aIntC < 11; aIntC++) {
        
        NSString *aStrDelete = [NSString stringWithFormat:@"Delete  from CollectionFilesMaster where collectionID = \'%d\'",aIntC];
        
        [SharedDatabase Delete:aStrDelete];
    }
}

#pragma mark  delegateMethod

- (void)FirstDefaultSelectPlayVideoSelected:(id)sender {
    
    NSLog(@"%@",sender);
    if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
        
        if (AppDelegateobj.isUpdateDownloading) {
            
            if (isUpdateInterrupted == 1) {
                btnRemainDownload.hidden = NO;
            } else {
                btnRemainDownload.hidden = YES;
            }
        } else {
            
            NSMutableArray *arrAllFile = [NSMutableArray array] ;
            NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
            
            [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
            
            
            if (arrAllFile.count > 0) {
                
                btnRemainDownload.hidden = NO;
                
            } else {
                btnRemainDownload.hidden = YES;
            }
        }
        
        
    } else {
        
        btnRemainDownload.hidden = YES;
    }
    
    [self SetTimerframeConterInvalidate];
    
    lblFramcounter.text=@"1";
    sliderTimeForVideo.value = 0.0;
    
    isPlaying = FALSE;
    [self.m_queueplayer pause];
    [self setPlayPauseImageForNormalState];
    imgFramecounter.hidden=NO;
    sliderTimeForVideo.enabled = YES;
    playPauseBtn.hidden = NO;
    loopBtn.hidden = NO;
    sliderSpeedControl.hidden = NO;
    //    [animation.view removeFromSuperview];
    //    [sender setSelected:NO];
    [btnInfo setEnabled:YES];
    [btnHelp setHidden:NO];
    [btnPreviousFrame setHidden:NO];
    [btnNextFrame setHidden:NO];
    [btnInfo setHidden:NO];
    [btnMenu setHidden:NO];
    
    sliderSpeedControl.enabled = YES;
    sliderTimeForVideo.enabled=YES;
    loopBtn.enabled = YES;
    playPauseBtn.enabled = YES;
    [btnPreviousFrame setEnabled:YES];
    [btnNextFrame setEnabled:YES];
    [btnMenu setEnabled:YES];
    
    
    [self LoadWbViewWithURL];
    
    
    if(arrPlayerItems) {
        
        arrPlayerItems=nil;
    }
    
    arrPlayerItems = [[NSMutableArray alloc]init ];
    int aintSelectedVideoIndex = 0;
    intcountofVideo=currentIndex=aintSelectedVideoIndex;
    
    if(appDelegate.arrScrollViewCount) {
        
        //        for(int i=0;i<[appDelegate.arrScrollViewCount count];i++) {
        
        NSInteger lenght = [(appDelegate.arrScrollViewCount)[0][0][@"fileTitle"] length];
        
        NSString *firstVideoPath;
        
        
        //            NSString *aStrThumbImage = [[AppDelegateobj.mutArrPlayVideo objectAtIndex:i]objectForKey:@"filePath"];
        //
        //            NSString *strImageName = [[[AppDelegateobj.mutArrPlayVideo objectAtIndex:i]objectForKey:@"filePath"] lastPathComponent];
        //
        //            if([aStrThumbImage rangeOfString:@"Update0001"].length > 0) {
        //
        //                NSString *dir_path = [NSString stringWithFormat:@"%@%@",Update_Path,strImageName];
        //                firstVideoPath = [dir_path stringByReplacingOccurrencesOfString:@"jpg" withString:@"mov"];
        //
        //            } else {
        
        //                if([aStrThumbImage rangeOfString:@"BiteFXiPadFull"].length > 0) {
        //
        //                    NSString *dir_path = [NSString stringWithFormat:@"%@%@",DEST_PATH,strImageName];
        //                    firstVideoPath = [dir_path stringByReplacingOccurrencesOfString:@"jpg" withString:@"mov"];
        //
        //                } else {
        
        firstVideoPath = [NSString stringWithFormat:@"%@",[[NSBundle mainBundle]
                                                           pathForResource:[(appDelegate.arrScrollViewCount)[0][0][@"fileTitle"] substringToIndex:lenght - 4] ofType:VIDEO_EXTENSION]];
        //                }
        //            }
        
        if (arrPlayerItems) {
            
            [arrPlayerItems addObject:firstVideoPath];//
            
        }
        //            break;
        //        }
    }
    NSLog(@"Play Seleceted Video path=> %@",arrPlayerItems[aintSelectedVideoIndex]);
    
    asset_ = [AVURLAsset URLAssetWithURL:[NSURL fileURLWithPath:arrPlayerItems[aintSelectedVideoIndex]] options:nil];
    [asset_ loadValuesAsynchronouslyForKeys:@[@"tracks"] completionHandler:^{
        
        // Get main Thread...
        dispatch_async(dispatch_get_main_queue(), ^{
            [self playVideoAtIndex:aintSelectedVideoIndex];
            [self.m_queueplayer pause];
            
        });
    }];
    
    //    lblFramcounter.text = @"1";
    [self setPlayPauseImageForNormalState];
    
    //Memory Leaks Commments
    //Make arrAllFile allocation if crashes
    NSMutableArray *arrAllFile = [NSMutableArray array];
    
    NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
    [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
    
    if (arrAllFile.count > 0) {
        
        AppDelegateobj.isFullversionDownloading = TRUE;
        AppDelegateobj.isUpdateDownloading = FALSE;
        return;
    }
    
    NSString *strSql = [NSString stringWithFormat:@"select * from FileUpdateList where IsDownloaded = 0"];
    //NSString *sql = @"select FileURL from FileUpdateList where IsDownloaded = 0";
    [arrAllFile addObjectsFromArray:[[Database shareDatabase] getAllDataForQuery:strSql]];
    
    if (arrAllFile.count > 0) {
        AppDelegateobj.isUpdateDownloading = TRUE;
        AppDelegateobj.isFullversionDownloading = FALSE;
        return;
    }
}

-(void)hideShowControlsIfImageOrVideo:(BOOL)MyValue
{
    sliderTimeForVideo.hidden = MyValue;
    playPauseBtn.enabled = !MyValue;
    loopBtn.enabled = !MyValue;
    sliderSpeedControl.enabled = !MyValue;
    imgFramecounter.hidden = MyValue;
    lblFramcounter.hidden = MyValue;
    img_SliderArea.hidden = MyValue;
}

-(void)mangePhotoVideoFlow:(NSInteger)index
{
    [btnFavUnfav setHidden:(userSubStat == NotSubscribed || appDelegate.intSelectedTab == 2)];
    
    if (userSubStat == Subscribed || userSubStat == InAppSubscribed) {
        
        if (AppDelegateobj.isUpdateDownloading) {
            
            if (isUpdateInterrupted == 1) {
                btnRemainDownload.hidden = NO;
            } else {
                btnRemainDownload.hidden = YES;
            }
        } else {
            NSMutableArray *arrAllFile = [NSMutableArray array] ;
            NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
            
            [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
            
            
            if (arrAllFile.count > 0) {
                
                btnRemainDownload.hidden = NO;
                
            } else {
                btnRemainDownload.hidden = YES;
            }
        }
        
        
    } else {
        
        btnRemainDownload.hidden = YES;
    }
    
    [self SetTimerframeConterInvalidate];
    
    lblFramcounter.text=@"1";
    sliderTimeForVideo.value = 0.0;
    
    isPlaying = FALSE;
    [self.m_queueplayer pause];
    [self setPlayPauseImageForNormalState];
    imgFramecounter.hidden=NO;
    sliderTimeForVideo.enabled = YES;
    playPauseBtn.hidden = NO;
    loopBtn.hidden = NO;
    sliderSpeedControl.hidden = NO;
    //    [animation.view removeFromSuperview];
    //    [sender setSelected:NO];
    [btnInfo setEnabled:YES];
    [btnHelp setHidden:NO];
    [btnPreviousFrame setHidden:NO];
    [btnNextFrame setHidden:NO];
    [btnInfo setHidden:NO];
    [btnMenu setHidden:NO];
    
    sliderSpeedControl.enabled = YES;
    sliderTimeForVideo.enabled=YES;
    loopBtn.enabled = YES;
    playPauseBtn.enabled = YES;
    [btnPreviousFrame setEnabled:YES];
    [btnNextFrame setEnabled:YES];
    [btnMenu setEnabled:YES];
    
    if(arrPlayerItems) {
        arrPlayerItems=nil;
    }
    
    //===================
    // Version 3.1 Changes
    //===================
    // Solve Prev - Next button is enable/disabled issue from client comment.
    // "Go to next item" button not disabled when you get to the end of an imported album of pictures:"
    // We have one extra object for add image so decrease count value to -1.
    
    NSString *aStrUserDefine = @"";
    if (appDelegate.selectedObj != nil) {
        aStrUserDefine = [NSString stringWithFormat:@"%@",(appDelegate.arrMedia)[0][@"isUserDefine"]];
    } else {
        aStrUserDefine = [NSString stringWithFormat:@"%@",(appDelegate.mutArrPlayVideo)[0][@"isUserDefine"]];
    }
    
    if ((appDelegate.intSelectedTab == 1) && ([aStrUserDefine isEqualToString:@"1"])) {
        [self removeAddButton];
    }
    
    arrPlayerItems = [[NSMutableArray alloc] init];
    NSInteger total = (AppDelegateobj.mutArrPlayVideo).count;
    NSInteger aintSelectedVideoIndex = index;
    intcountofVideo=currentIndex=aintSelectedVideoIndex;
    
    //Rohan Condition
    if (total == 1) {
        [btnNextFrame setEnabled:NO];
        [btnPreviousFrame setEnabled:NO];
    }else if (aintSelectedVideoIndex == total-1) {
        [btnNextFrame setEnabled:NO];
        [btnPreviousFrame setEnabled:YES];
    }else{
        if (aintSelectedVideoIndex == 0) {
            [btnNextFrame setEnabled:YES];
            [btnPreviousFrame setEnabled:NO];
        }else{
            [btnNextFrame setEnabled:YES];
            [btnPreviousFrame setEnabled:YES];
        }
    }
    
    if (appDelegate.selectedObj != nil) {
        (AppDelegateobj.arrMedia)[0][@"isPlayed"] = @"1";
        
        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  fileID=\'%@\'",(AppDelegateobj.arrMedia)[0][@"filesID"]];
        [SharedDatabase Update:aStrUpdateSql];
        
        NSString *firstVideoPath;
        NSString *aStrThumbImage = (AppDelegateobj.arrMedia)[0][@"filePath"];
        firstVideoPath = aStrThumbImage;
        if (arrPlayerItems) {
            if(firstVideoPath) {
                [arrPlayerItems addObject:firstVideoPath];
            }
        }
    } else {
        (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
        
        NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  fileID=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filesID"]];
        [SharedDatabase Update:aStrUpdateSql];
        
        if(AppDelegateobj.mutArrPlayVideo) {
            for(int i=0;i<total;i++) {
                NSString *firstVideoPath;
                NSString *aStrThumbImage = (AppDelegateobj.mutArrPlayVideo)[i][@"filePath"];
                firstVideoPath = aStrThumbImage;
                if (arrPlayerItems) {
                    if(firstVideoPath) {
                        [arrPlayerItems addObject:firstVideoPath];
                    }
                }
            }
        }
    }
    
    if (appDelegate.isPresentationInfoOpen) {
        lbl_view_title.frame = CGRectMake(324, 0 , 605 , 53);
    }
    
    
    AppDelegateobj.intSelectedVideo = currentIndex;
    
    NSString *mediaType = appDelegate.selectedObj != nil ?  appDelegate.arrMedia[0][@"fileType"] : AppDelegateobj.mutArrPlayVideo[aintSelectedVideoIndex][@"fileType"];
    
    NSString *isHide = appDelegate.selectedObj != nil ?  appDelegate.arrMedia[0][@"IsHidden"] : (AppDelegateobj.mutArrPlayVideo)[aintSelectedVideoIndex][@"IsHidden"];
    
    NSString *isUserDefine = appDelegate.selectedObj != nil ?  appDelegate.arrMedia[0][@"isUserDefine"] : (AppDelegateobj.mutArrPlayVideo)[aintSelectedVideoIndex][@"isUserDefine"];
    
    if ([mediaType isEqualToString:@"MOVIE"]) {
        isImageDisplay = NO;
        btnHideUnhideImages.hidden = YES;
        NSLog(@"Play Seleceted Video path=> %@",arrPlayerItems[aintSelectedVideoIndex]);
        [self showHideFileTitleWithAnimation];
        [self LoadWbViewWithURL];
        m_playerView.hidden = NO;
        [btnFlip setHidden:NO];
        imgview_fullImage.hidden = YES;
        asset_ = [AVURLAsset URLAssetWithURL:[NSURL fileURLWithPath:arrPlayerItems[aintSelectedVideoIndex]] options:nil];
        [asset_ loadValuesAsynchronouslyForKeys:@[@"tracks"] completionHandler:^{
            // Get main Thread...
            dispatch_async(dispatch_get_main_queue(), ^{
                [self playVideoAtIndex:aintSelectedVideoIndex];
                [self.m_queueplayer pause];
                
            });
        }];
        [self hideShowControlsIfImageOrVideo:NO];
        //    lblFramcounter.text = @"1";
        [self setPlayPauseImageForNormalState];
        
        //Memory Leaks Commments
        //Make arrAllFile allocation if crashes
        NSMutableArray *arrAllFile = [NSMutableArray array];
        
        
        NSString *sql = @"select FileURL from FileDownloadList where IsDownloaded = 0";
        [arrAllFile addObjectsFromArray:[[Database shareDatabase] getFirstColumnForQuery:sql]];
        
        if (arrAllFile.count > 0) {
            
            AppDelegateobj.isFullversionDownloading = TRUE;
            AppDelegateobj.isUpdateDownloading = FALSE;
            
            //===================
            // Version 2.4 Changes
            //===================
            // Comment return line...
            //            return;
        }
        else
        {
            // Code in else part because return line is commented...
            NSString *strSql = [NSString stringWithFormat:@"select * from FileUpdateList where IsDownloaded = 0"];
            //NSString *sql = @"select FileURL from FileUpdateList where IsDownloaded = 0";
            [arrAllFile addObjectsFromArray:[[Database shareDatabase] getAllDataForQuery:strSql]];
            
            if (arrAllFile.count > 0) {
                
                AppDelegateobj.isUpdateDownloading = TRUE;
                AppDelegateobj.isFullversionDownloading = FALSE;
                //===================
                // Version 2.4 Changes
                //===================
                // Comment return line...
                //            return;
            }
            
        }
        
        if (appDelegate.isPresentationInfoOpen) {
            
            if([self.view.subviews containsObject:wbView])
            {
                [self removeWebView];
            }
            
        }
        
    }
    else
    {
        isImageDisplay = YES;
        [self hideShowControlsIfImageOrVideo:YES];
        m_playerView.hidden = YES;
        [btnFlip setHidden:YES];
        imgview_fullImage.hidden = NO;
        btnInfo.enabled = NO;
        btnHideUnhideImages.hidden = NO;
        if([self.view.subviews containsObject:wbView])
        {
            [self removeWebView];
        }
        btnHideUnhideImages.userInteractionEnabled = YES;
        btnHideUnhideImages.alpha = 1.0;
        
        if([isUserDefine isEqualToString:@"1"])
        {
            if ([isHide isEqualToString:@"1"])
            {
                [self manageAlignmentTitleLabel];
                lbl_view_title.alpha = 0.0;
                imgview_fullImage.image = [UIImage imageNamed:@"hiddenpic.png"];
                btnHideUnhideImages.selected = NO;
            }
            else
            {
                [self showHideFileTitleWithAnimation];
                imgview_fullImage.image = [UIImage imageWithContentsOfFile:arrPlayerItems[aintSelectedVideoIndex]];
                btnHideUnhideImages.selected = YES;
            }
            
        }
        else
        {
            [self showHideFileTitleWithAnimation];
            imgview_fullImage.image = [UIImage imageWithContentsOfFile:arrPlayerItems[aintSelectedVideoIndex]];
            btnHideUnhideImages.userInteractionEnabled = NO;
            btnHideUnhideImages.alpha = 0.5;
        }
        
        NSString *favourite = [NSString stringWithFormat:@"%@",appDelegate.selectedObj != nil ?  appDelegate.arrMedia[0][@"isFavourite"] : (appDelegate.mutArrPlayVideo)[currentIndex][@"isFavourite"]];
        [btnFavUnfav setSelected:[favourite isEqualToString:@"1"]];
    }
    
    //===================
    // Version 2.4 Changes
    //===================
    if (appDelegate.intSelectedTab == 2 && appDelegate.isPresentationInfoOpen) {
        [self showPresentationInfo];
    }
    
    [UIView transitionWithView:self->btnFlip
                      duration:0.3
                       options:UIViewAnimationOptionTransitionCrossDissolve
                    animations:^{
        [self->btnFlip setSelected:NO];
    } completion:nil];
}

- (void)removeAddButton {
    
    NSString *aStrUserDefine = [(appDelegate.mutArrPlayVideo)[0] valueForKey:@"isUserDefine"];
    
    if(appDelegate.intSelectedTab == 1 && [aStrUserDefine isEqualToString:@"1"])
    {
        // Remove add button object...
        NSString *aStrCollectionId = [appDelegate.mutArrPlayVideo.lastObject valueForKey:@"collectionID"];
        if ([aStrCollectionId isEqualToString:@"-100"]) {
            [appDelegate.mutArrPlayVideo removeLastObject];
        }
        
    }
}

- (void)PlayVideoSelected:(id)sender {
    if([self.view.subviews containsObject:wbView])
    {
        [self removeWebView];
    }
    
    [self manageBtnGlobleShowHideImg];
    [self mangePhotoVideoFlow:AppDelegateobj.intSelectedVideo];
}

-(void)manageBtnGlobleShowHideImg
{
    bool isDisplyGlobleShowHideBtn = true;
    
    if (appDelegate.selectedObj != nil) {
        
    } else {
        for (int i=0; i< appDelegate.mutArrPlayVideo.count; i++) {
            NSDictionary *objDict = appDelegate.mutArrPlayVideo[i];
            if ([objDict[@"IsHidden"] isEqualToString:@"0"] ) {
                isDisplyGlobleShowHideBtn = false;
            }
        }
    }
    
    if (appDelegate.intSelectedTab == 1)
    {
        if (isDisplyGlobleShowHideBtn)
        {
            
            btnHideUnhideImages.selected = NO;
        }
        else
        {
            btnHideUnhideImages.selected = YES;
        }
        btnHideUnhideImages.hidden = NO;
    }
    else
    {
        btnHideUnhideImages.hidden = YES;
    }
    
    
}

//infoHtml
- (NSString*)getHtmlString:(NSString*)aStrhtml {
    
    if (AppDelegateobj.boolSelectedPlaylist == YES) {
        
        if ((AppDelegateobj.mutArrPlayVideo).count > 0) {
            
            NSString *aStrFileTitle = (AppDelegateobj.mutArrPlayVideo)[AppDelegateobj.intSelectedVideo][@"fileTitle"];
            
            NSString *aStrQuery = [NSString stringWithFormat:@"select infoFilePath from LocalFileMaster where fileTitle = \'%@\'", aStrFileTitle];
            
            NSMutableArray *aMutArrResult = [SharedDatabase getAllDataForQuery:aStrQuery];
            
            //------ For info Path ----------//
            NSString *aStrInfoPathTemp = aMutArrResult[0][@"infoFilePath"];
            [aMutArrResult[0]removeObjectForKey:@"infoFilePath"];
            
            NSString *aStrThumbnailPath = [[NSBundle mainBundle]pathForResource:aStrInfoPathTemp ofType:@"html"];
            
            if ([[NSFileManager defaultManager] fileExistsAtPath:aStrThumbnailPath])
                
            {
                aMutArrResult[0][@"infoFilePath"] = aStrThumbnailPath;
                
            }
            
            else
                
            {
                aMutArrResult[0][@"infoFilePath"] = [NSString stringWithFormat:@"%@/%@",DOCUMENT_DIRECTORY_PATH,aStrInfoPathTemp];
                
            }
            
            
            
            return aMutArrResult[0][@"infoFilePath"];
        }
        
    } else {
        
        if ((AppDelegateobj.mutArrPlayVideo).count > 0) {
            
            
            NSString *aStrFileTitle = appDelegate.selectedObj != nil ? (AppDelegateobj.arrMedia)[0][@"fileTitle"] : (AppDelegateobj.mutArrPlayVideo)[AppDelegateobj.intSelectedVideo][@"fileTitle"];
            
            NSString *aStrQuery = [NSString stringWithFormat:@"select infoFilePath from LocalFileMaster where fileTitle = \'%@\'", aStrFileTitle];
            
            NSMutableArray *aMutArrResult = [SharedDatabase getAllDataForQuery:aStrQuery];
            
            //===================
            // Version 3.0 Changes
            //===================
            // Condition is added to solve crash issue...
            if (aMutArrResult.count > 0) {
                
                //------ For info Path ----------//
                NSString *aStrInfoPathTemp = aMutArrResult[0][@"infoFilePath"];
                [aMutArrResult[0]removeObjectForKey:@"infoFilePath"];
                
                NSString *aStrThumbnailPath = [[NSBundle mainBundle]pathForResource:aStrInfoPathTemp ofType:@"html"];
                
                if ([[NSFileManager defaultManager] fileExistsAtPath:aStrThumbnailPath]) {
                    aMutArrResult[0][@"infoFilePath"] = aStrThumbnailPath;
                    
                }
                else {
                    aMutArrResult[0][@"infoFilePath"] = [NSString stringWithFormat:@"%@/%@",DOCUMENT_DIRECTORY_PATH,aStrInfoPathTemp];
                }
                
                return aMutArrResult[0][@"infoFilePath"];
            }
        }
    }
    
    return [[NSBundle mainBundle] pathForResource:@"GoodOpenNoMusclesSide" ofType:@"html"];
}


#pragma mark - Get First Collection Video Details

- (void)getFirstCollectionVideoDetail {
    
    // Get first collectionId...
    NSString *aStrQuery = [NSString stringWithFormat:@"select collectionID from CollectionMaster where fileType = \'MOVIE\' Order By isUserDefine Asc,  fileOrder LIMIT 1"];
    
    NSMutableArray *aMutArrCollectionIds = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
    
    NSString *aStrCollectionId = @"1";
    if (aMutArrCollectionIds.count > 0) {
        aStrCollectionId = [aMutArrCollectionIds[0] valueForKey:@"collectionID"];
    }
    
    //    NSString *aStrCollQuery = [NSString stringWithFormat:@"select filesID from CollectionFilesMaster where collectionID ='%@' ORDER BY filesID ASC", aStrCollectionId];
    
    // Version 2.4 New change because we delete old data...
    NSString *aStrCollQuery = [NSString stringWithFormat:@"select filesID from CollectionFilesMaster where collectionID ='%@' ORDER BY order_file", aStrCollectionId];
    
    NSMutableArray *aMutArrFilesID = [SharedDatabase getAllDataForQuery:aStrCollQuery];
    
    NSMutableArray *aMutArrData = [[NSMutableArray alloc] init];
    
    if (aMutArrFilesID.count>0) {
        
        if (arrPlayerItems) {
            [arrPlayerItems removeAllObjects];
        }
        
        for (int aIntCnt = 0; aIntCnt <aMutArrFilesID.count; aIntCnt++)
        {
            
            NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from LocalFileMaster where fileID = \'%@\'",aMutArrFilesID[aIntCnt][@"filesID"]];
            
            NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
            
            NSInteger aLength = [aMutArrNames[0][@"fileTitle"] length];
            
            NSString *aStrThumbImage = aMutArrNames[0][@"filePath"];
            
            NSString *strImageName = [aMutArrNames[0][@"filePath"] lastPathComponent];
            
            NSString *firstVideoPath = @"";
            
            NSString *aStrMovieImgThumbPath = [NSString stringWithFormat:@"%@/%@/%@", BITEFXV2, THUMBNAILS, MOVIES];
            
#pragma mark --------------------------------
#pragma mark Version 2.4 Folder Structure Changes
            
            if([aStrThumbImage rangeOfString:aStrMovieImgThumbPath].length > 0) {
                
                firstVideoPath = [NSString stringWithFormat:@"%@/%@", aStrMovieImgThumbPath, strImageName];
            }
            else if([aStrThumbImage rangeOfString:@"BiteFXiPadFull"].length>0) {
                
                NSString *dir_path = [NSString stringWithFormat:@"%@%@",DEST_PATH,strImageName];
                firstVideoPath = [NSString stringWithFormat:@"%@",dir_path];
                
            }  else if([aStrThumbImage rangeOfString:@"BiteFXV2"].length > 0) {
                
                NSString *dir_path = [NSString stringWithFormat:@"%@/%@/%@/%@", DOCUMENT_DIRECTORY_PATH, BITEFXV2, MOVIES , strImageName];
                firstVideoPath = [NSString stringWithFormat:@"%@",dir_path];
                
            }  else if([aStrThumbImage rangeOfString:@"Update0001"].length > 0) {
                
                NSString *dir_path = [NSString stringWithFormat:@"%@%@",Update_Path,strImageName];
                firstVideoPath = [NSString stringWithFormat:@"%@",dir_path];
                
                
            } else {
                
                firstVideoPath = [[NSBundle mainBundle] pathForResource:[aMutArrNames[0][@"fileTitle"] substringToIndex:aLength - 4] ofType:VIDEO_EXTENSION];
            }
            
            
            if(arrPlayerItems) {
                
                if (firstVideoPath && ![firstVideoPath isEqual:[NSNull null]]) {
                    
                    [arrPlayerItems addObject:firstVideoPath];
                    
                } else {
                    
                    [arrPlayerItems addObject:@""];
                }
            }
            
            // Set whole filePath with document directory path...
            aMutArrNames.firstObject[@"filePath"] = firstVideoPath;
            // Add values in array for further use...
            [aMutArrData addObject:aMutArrNames.firstObject];
        }
        
        //Rohan
        //        appDelegate.mutArrPlayVideo = arrPlayerItems;
        
        //===================
        // Version 3.1 Changes
        //===================
        // Solve Next button is disabled issue from client comment.
        //        [btnPreviousFrame setEnabled:NO];
        //        [btnNextFrame setEnabled:NO];
        
        if (arrPlayerItems.count == 1) {
            [btnPreviousFrame setEnabled:NO];
            [btnNextFrame setEnabled:NO];
        }else{
            [btnPreviousFrame setEnabled:NO];
            [btnNextFrame setEnabled:YES];
        }
        
        // Assign values to mutArrPlayVideo...
        if (appDelegate.mutArrPlayVideo.count == 0) {
            appDelegate.mutArrPlayVideo = aMutArrData;
        }
        
        if (appDelegate.mutArrPlayVideo.count > 0) {
            NSString *favourite = [NSString stringWithFormat:@"%@",(appDelegate.mutArrPlayVideo)[currentIndex][@"isFavourite"]];
            [btnFavUnfav setSelected:[favourite isEqualToString:@"1"]];
        }
    }
}

#pragma mark--
#pragma mark ProgressBar
- (void)progressBarmoved:(float)value {
    
    //  [progressBar setTrackImage:[UIImage imageNamed:@"progressbarNormal.png"]];
    lblProgresspercent.hidden = NO;
    lblProgresspercent.text = @"";
    progressBar.hidden = NO;
    NSString *aStrprogresstext;
    
    int aIntProgressPercent = (int)value;
    if(FullorUpdate==1)
    {
        aStrprogresstext = [NSString stringWithFormat:@"%d",aIntProgressPercent];
    }
    else {
        
        aStrprogresstext = [NSString stringWithFormat:@"%d",aIntProgressPercent];
        
    }
    lblProgresspercent.text = [aStrprogresstext stringByAppendingString:@"% completed"];
    
    progressBar.progressViewStyle = UIProgressViewStyleDefault;
    progressBar.progress = aIntProgressPercent/100;
    
}


#pragma mark - FileDownload

#pragma mark--
#pragma mark Delegate Methods of VideoDownloading Progress

- (void)downLoadFile {
    
    [UIApplication sharedApplication].idleTimerDisabled  = YES;
    isUpdatedJpgDownloading = FALSE;
    isUpdatedHtmldownloading = FALSE;
    isJpgDownoad = FALSE;
    
    appDelegate.wasDownloadinProgress = TRUE;
    
    if(FullorUpdate==0)
    {
        [UserDefaults synchronize];
        
        if((self.ArrUpdatedownload).count>intUpdateCounter)
        {
            NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@/%@",DownloadFileURL,(self.ArrUpdatedownload)[intUpdateCounter][@"version_number"],(self.ArrUpdatedownload)[intUpdateCounter][@"file"]];
            
            NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",Update_Path,(self.ArrUpdatedownload)[intUpdateCounter][@"file"]];
            
            [self setNoOfVideoCompleted:[NSString stringWithFormat:@"Downloading %d of %ld file(s)...", intUpdateCounter+1,(long)(self.ArrUpdatedownload).count]];
            
            if ([[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
            {
                [[NSFileManager defaultManager] removeItemAtPath:strVideoPath error:nil];
            }
            
            if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
            {
                self.downloadManager.delegate = self;
                
                NSURL *url1 = [NSURL URLWithString:strVideoURL];
                [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
            }
            else
            {
                if(intUpdateCounter == (self.ArrUpdatedownload).count-1)
                {
                    [self downloadingUpdatedHtml];
                }
                else
                {
                    intUpdateCounter++;
                    
                    if((self.ArrUpdatedownload).count>intUpdateCounter)
                    {
                        
                        [self setNoOfVideoCompleted:[NSString stringWithFormat:@"Downloading %d of %ld file(s)...",intUpdateCounter+1,(self.ArrUpdatedownload).count*3]];
                        
                    }
                    
                    [self downLoadFile];
                    
                }
            }
        }
    }
    else if(FullorUpdate==1)
    {
        if ((self.mutArrFullVersion).count > intVideoCounter)
        {
            appDelegate.wasDownloadinProgress = TRUE;
            NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,(self.mutArrFullVersion)[intVideoCounter][@"file"]];
            
            NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",DEST_PATH,(self.mutArrFullVersion)[intVideoCounter][@"file"]];
            
            lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intVideoCounter+1,(self.mutArrFullVersion).count*3];
            
            if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
            {
                self.downloadManager.delegate = self;
                
                NSURL *url1 = [NSURL URLWithString:strVideoURL];
                [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
                
            }
            
            else
            {
                if(intVideoCounter==(self.mutArrFullVersion).count-1)
                {
                    [self downLoadHTML];
                    
                }
                else
                {
                    intVideoCounter++;
                    
                    if((self.mutArrFullVersion).count>intVideoCounter)
                    {
                        lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intVideoCounter+1,(self.mutArrFullVersion).count*3];
                        
                    }
                    
                    [self downLoadFile];
                    
                }
                
            }
            
        }
        
    }
    
    
}

- (void)downLoadHTML {
    
    isHtmlDownload = TRUE;
    isJpgDownoad = FALSE;
    FullorUpdate=2;
    if ((self.mutArrFullVersion).count > intHtmlFileCounter)
    {
        NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,(self.mutArrFullVersion)[intHtmlFileCounter][@"html"]];
        
        NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",DEST_PATH,(self.mutArrFullVersion)[intHtmlFileCounter][@"html"]];
        
        
        
        
        lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intVideoCounter+intHtmlFileCounter+1+1,(self.mutArrFullVersion).count*3];
        
        if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            self.downloadManager.delegate = self;
            NSURL *url1 = [NSURL URLWithString:strVideoURL];
            [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
        }
        else
        {
            if(intHtmlFileCounter==(self.mutArrFullVersion).count-1)
            {
                [self downLoadimgImage];
            }
            else
            {
                intHtmlFileCounter++;
                if((self.mutArrFullVersion).count>intHtmlFileCounter)
                {
                    
                    lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intVideoCounter+intHtmlFileCounter+1+1,(self.mutArrFullVersion).count*3];
                }
                [self downLoadHTML];
            }
        }
    }
}
- (void)downLoadimgImage {
    
    isJpgDownoad = TRUE;
    isHtmlDownload = FALSE;
    FullorUpdate=3;
    if ((self.mutArrFullVersion).count > intJpgFileCounter)
    {
        appDelegate.wasDownloadinProgress = TRUE;
        NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,(self.mutArrFullVersion)[intJpgFileCounter][@"img"]];
        
        NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",DEST_PATH,(self.mutArrFullVersion)[intJpgFileCounter][@"img"]];
        
        
        
        lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intVideoCounter+intHtmlFileCounter+intJpgFileCounter+3,(self.mutArrFullVersion).count*3];
        if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            self.downloadManager.delegate = self;
            
            NSURL *url1 = [NSURL URLWithString:strVideoURL];
            [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
        }
        else
        {
            if(intJpgFileCounter==(self.mutArrFullVersion).count-1)
            {
                [self DownloadFullVersionMVI];
            }
            else
            {
                intJpgFileCounter++;
                
                if((self.mutArrFullVersion).count>intJpgFileCounter)
                {
                    lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intVideoCounter+intHtmlFileCounter+intJpgFileCounter+3,(self.mutArrFullVersion).count*3];
                }
                
                [self downLoadimgImage];
                
            }
            
        }
        
    }
    
}


-(void)downloadingUpdatedHtml
{
    FullorUpdate=9;
    isUpdatedHtmldownloading = TRUE;
    isJpgDownoad = FALSE;
    isHtmlDownload = FALSE;
    isUpdatedJpgDownloading = FALSE;
    
    
    if((self.ArrUpdatedownload).count>inthtmlUpdateCounter)
    {
        NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@/%@",DownloadFileURL,(self.ArrUpdatedownload)[inthtmlUpdateCounter][@"version_number"],(self.ArrUpdatedownload)[inthtmlUpdateCounter][@"html"]];
        
        NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",Update_Path,(self.ArrUpdatedownload)[inthtmlUpdateCounter][@"html"]];
        
        
        if ([[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            
            [[NSFileManager defaultManager] removeItemAtPath:strVideoPath error:nil];
        }
        
        if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            self.downloadManager.delegate = self;
            
            NSURL *url1 = [NSURL URLWithString:strVideoURL];
            [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
        }
        
        
        else
        {
            if(inthtmlUpdateCounter == (self.ArrUpdatedownload).count-1)
            {
                [self downloadingUpdatedJpg];
            }
            
            else
            {
                inthtmlUpdateCounter++;
                
                if((self.ArrUpdatedownload).count>inthtmlUpdateCounter)
                {
                    
                    
                    lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intUpdateCounter+inthtmlUpdateCounter+1+1,(self.ArrUpdatedownload).count*3];
                    
                }
                
                [self downloadingUpdatedHtml];
                
            }
            
        }
        
    }
    
    
}

-(void)downloadingUpdatedJpg
{
    FullorUpdate=9;
    isUpdatedHtmldownloading = FALSE;
    isJpgDownoad = FALSE;
    isHtmlDownload = FALSE;
    isUpdatedJpgDownloading = TRUE;
    
    if((self.ArrUpdatedownload).count>intJpgUpdateCounter)
    {
        NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@/%@",DownloadFileURL,(self.ArrUpdatedownload)[intJpgUpdateCounter][@"version_number"],(self.ArrUpdatedownload)[intJpgUpdateCounter][@"img"]];
        
        NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",Update_Path,(self.ArrUpdatedownload)[intJpgUpdateCounter][@"img"]];
        
        if ([[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            
            [[NSFileManager defaultManager] removeItemAtPath:strVideoPath error:nil];
        }
        
        
        if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            self.downloadManager.delegate = self;
            
            NSURL *url1 = [NSURL URLWithString:strVideoURL];
            [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
        }
        
        
        else
        {
            if(intJpgUpdateCounter == (self.ArrUpdatedownload).count-1)
            {
                [self DownloadingMviFiles];
            }
            else
            {
                intJpgUpdateCounter++;
                
                if((self.ArrUpdatedownload).count>intJpgUpdateCounter)
                {
                    
                    
                    lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intUpdateCounter+inthtmlUpdateCounter+intJpgUpdateCounter+1+1+1,(self.ArrUpdatedownload).count*3];
                }
                [self downloadingUpdatedJpg];
            }
        }
    }
}
- (void) dataDownloadAtPercent: (NSNumber *) aPercent
{
    // Version 3.0 change - Full doawnload crash issue
    // When MVI file is downloading no need to show progressbar...
    if(isFullVersionXML) {
        return;
    }
    progressBar.hidden = NO;
    [self progressBarmoved:(aPercent.floatValue*100) ];
    progressBar.progress = aPercent.floatValue;
}


- (void)removeDownloadView
{
    if(viewDownloadingData)
    {
        [viewDownloadingData removeFromSuperview];
    }
}

- (void) dataDownloadFailed: (NSString *) reason {
    [self updateInterfaceWithReachability:hostReach];
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    
    if(FullorUpdate==1) {
        
        //===================
        // Version 2.4 Changes
        //===================
        // "savePath" is not used so code commented...
        //        [[self.mutArrFullVersion objectAtIndex:intVideoCounter] setObject:@"" forKey:@"savePath"];
        intVideoCounter++;
        
        [self downLoadFile];
    }
    else {
        
        if(FullorUpdate==0)
        {
            if((self.ArrUpdatedownload).count>intUpdateCounter){
                
                (self.ArrUpdatedownload)[intUpdateCounter][@"savePath"] = @"";
                [self downLoadFile];
                intUpdateCounter++;
            }else{
                NSLog(@"Done");
            }
        }
        if(isUpdatedHtmldownloading)
        {
            (self.ArrUpdatedownload)[inthtmlUpdateCounter][@"savePath"] = @"";
            inthtmlUpdateCounter++;
            [self downloadingUpdatedHtml];
            
        }
        
        if(isUpdatedJpgDownloading)
        {
            (self.ArrUpdatedownload)[intJpgUpdateCounter][@"savePath"] = @"";
            intJpgUpdateCounter++;
            [self downloadingUpdatedJpg];
        }
        
    }
}

- (void) didReceiveFilename: (NSString *) aName {
    
    NSString *savePath = [DEST_PATH stringByAppendingString:aName];
    
    if(isFullVersionXML) {
        
    }
    if(FullorUpdate==1) {
        
        //===================
        // Version 2.4 Changes
        //===================
        // "savePath" is not used so code commented...
        
        //        if ([self.mutArrFullVersion count] > intVideoCounter) {
        //            [[self.mutArrFullVersion objectAtIndex:intVideoCounter] setObject:savePath forKey:@"savePath"];
        //        }
    } else {
        NSString *savePath = [Update_Path stringByAppendingString:aName];
        if(FullorUpdate==0) {
            if((self.ArrUpdatedownload).count>intUpdateCounter){
                (self.ArrUpdatedownload)[intUpdateCounter][@"savePath"] = savePath;
            }
        }
        if(isUpdatedHtmldownloading) {
            if((self.ArrUpdatedownload).count>inthtmlUpdateCounter) {
                (self.ArrUpdatedownload)[inthtmlUpdateCounter][@"savePath"] = savePath;
            }
        }
        if(isUpdatedJpgDownloading) {
            if((self.ArrUpdatedownload).count>intJpgUpdateCounter) {
                (self.ArrUpdatedownload)[intJpgUpdateCounter][@"savePath"] = savePath;
            }
        }
        if(isMviDownloading) {
            if((self.mutArrTotalmviFiles).count>intMviCounter) {
                (self.arrMviFile)[intMviCounter][@"savePath"] = savePath;
            }
        }
    }
    if(isHtmlDownload) {
        if ((self.mutArrFullVersion).count > intHtmlFileCounter) {
            (self.mutArrFullVersion)[intHtmlFileCounter][@"savePath"] = savePath;
        }
    }
    if(isJpgDownoad) {
        if ((self.mutArrFullVersion).count > intJpgFileCounter){
            (self.mutArrFullVersion)[intJpgFileCounter][@"savePath"] = savePath;
        }
    }
}

- (void) didReceiveData: (NSMutableData *) theData {
    if(isFullVersionXML) {
        
        NSLog(@"Inside didReceiveData and isFullVersionXML");
        //        NSString *path  = [AppDelegate BitFXFull];
        
#pragma mark --------------------------------
#pragma mark Version 2.4 Folder Structure Changes
        
        NSString *aStrDirPath = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:BITEFXV2];
        NSString *aStrMvi  = [NSString stringWithFormat:@"%@/BiteFXiPadFullInventory.mvi", aStrDirPath];
        
        if(![theData writeToFile:aStrMvi atomically:YES]) {
            // NSLog(@"File hasn't been saved.");
        } else {
            [self setAttribDonotMarkiCloudSetting:aStrMvi];
        }
        appDelegate.wasDownloadinProgress = FALSE;
        lblNoofVideoCompleted.text = @"";
        lblfullorupdate.text = @"Full-version download completed";
        intFullversiondownloadCompleted = 1;
        progressBar.hidden = YES;
        [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
        [UserDefaults setBool:YES forKey:@"FullDownloaded"];
        [UserDefaults synchronize];
        UpdateCompleted = TRUE;
        isFullVersionDownloaded = TRUE;
        
        NSMutableArray *aTempArray = [[Database shareDatabase]getAllDataForQuery:@"select * from FileDownloadList where isSkipFile = '1'"];
        NSString *aTempString;
        if(aTempArray.count == 0)
        {
            aTempString = defineFileDownloadSuccessMessage;
        }
        else
        {
            aTempString = [NSString stringWithFormat:@"All but %lu files were downloaded successfully",(unsigned long)aTempArray.count];
        }
        lblProgresspercent.text = aTempString;
        AppDelegateobj.isDownloadCancelled = false;
        [self startXMLParsing];
        
        //===================
        // Version 3.1 Changes
        //===================
        // Feature MVI file changes.
        //-----------------
        // Download Featues.MVI file if it is not downloaded previously due to any error or issue...
        [self downloadFeaturesMVI];
        //===================
        
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        isFullVersionXML = FALSE;
        
        [UserDefaults  setBool:NO forKey:@"isDowmloadCancelled"];
        [UserDefaults  synchronize];
        
        if (userSubStat == InAppSubscribed) {
            if ((appDelegate.mutArrDataItem).count > 0) {
                [self saveDBForInApp];
            }
        }
        
        [self onClickCheckForUpdate:nil];
        
        // Version 3.0 changes
        // Download success message should appear untill user clicks on close icon.
        // Check if all files downloaded successfully, show download message otherwise remove downloaddataview...
        NSMutableArray *arrRemainFileDownloadList = [AppDelegate listOfRemainFileDownload];
        if (arrRemainFileDownloadList.count > 0 && appDelegate.isFullversionDownloading) {
            [viewDownloadingData removeFromSuperview];
        }
        
    }
    
    if(FullorUpdate==1) {
        [self saveData:theData forURL:self.downloadManager.filename];
    } else if(FullorUpdate==0) {
        [self saveUpdateData:theData forURL:self.downloadManager.filename];
        
        
    } else if(isUpdatedHtmldownloading) {
        
        if ((self.ArrUpdatedownload).count > inthtmlUpdateCounter) {
            
            if (![theData writeToFile:(self.ArrUpdatedownload)[inthtmlUpdateCounter][@"savePath"] atomically:YES]) {
                
                NSLog(@"File hasn't been saved.");
                
            } else {
                [self setAttribDonotMarkiCloudSetting:(self.ArrUpdatedownload)[inthtmlUpdateCounter][@"savePath"]];
            }
            lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intUpdateCounter+inthtmlUpdateCounter+1+1,(self.ArrUpdatedownload).count*3];
        }
        
        if(inthtmlUpdateCounter==(self.ArrUpdatedownload).count-1) {
            
            [self.downloadManager cleanup];
            [self downloadingUpdatedJpg];
            
        } else {
            
            [self.downloadManager cleanup];
            inthtmlUpdateCounter++;
            [self downloadingUpdatedHtml];
        }
        
    } else if(isUpdatedJpgDownloading) {
        
        if ((self.ArrUpdatedownload).count > intJpgUpdateCounter) {
            
            if (![theData writeToFile:(self.ArrUpdatedownload)[intJpgUpdateCounter][@"savePath"] atomically:YES]) {
                
                NSLog(@"File hasn't been saved.");
                
            } else {
                [self setAttribDonotMarkiCloudSetting:(self.ArrUpdatedownload)[intJpgUpdateCounter][@"savePath"]];
                
            }
            
            lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...", intUpdateCounter+inthtmlUpdateCounter+intJpgUpdateCounter+1+1+1,(self.ArrUpdatedownload).count*3];
        }
        
        if(intJpgUpdateCounter==(self.ArrUpdatedownload).count-1) {
            
            [UIApplication sharedApplication].idleTimerDisabled = NO;
            [self.downloadManager cleanup];
            [self DownloadingMviFiles];
            
        } else {
            
            [self.downloadManager cleanup];
            intJpgUpdateCounter++;
            [self downloadingUpdatedJpg];
        }
        
    } else if(isMviDownloading) {
        
        if ((self.mutArrTotalmviFiles).count > intMviCounter) {
            
            if (![theData writeToFile:(self.arrMviFile)[intMviCounter][@"savePath"] atomically:YES]) {
                
                NSLog(@"File hasn't been saved.");
            } else {
                
                [self setAttribDonotMarkiCloudSetting:(self.arrMviFile)[intMviCounter][@"savePath"]];
            }
        }
        
        if(intMviCounter==(self.mutArrTotalmviFiles).count-1) {
            
            UpdateCompleted = TRUE;
            
            NSString *aStrSql = @"Update UpdatesList set UpdateDownloaded = '1'";
            [SharedDatabase Update:aStrSql];
            
            appDelegate.wasDownloadinProgress = FALSE;
            intUpdateCounter=inthtmlUpdateCounter=intJpgUpdateCounter=intMviCounter=0;
            
            lblNoofVideoCompleted.text = @"";
            intupdate=0;
            intUpdatesdownloadCompleted = 1;
            intParsingCounter = 1;
            progressBar.hidden = YES;
            lblfullorupdate.text = @"Updates downloaded";
            [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
            [UserDefaults setBool:YES forKey:@"UpdateDownloaded"];
            [UserDefaults synchronize];
            isUpdateDownloaded = TRUE;
            lblProgresspercent.text = @"Updates downloaded successfully";
            AppDelegateobj.isDownloadCancelled = false;
            [self startXMLParsingUpdated];
            //            [self CheckingUpdates];
            [UIApplication sharedApplication].idleTimerDisabled = NO;
            
            if (userSubStat == InAppSubscribed) {
                if ((appDelegate.mutArrDataItem).count > 0) {
                    [self saveDBForInApp];
                }
            }
        } else {
            intMviCounter++;
            [self.downloadManager cleanup];
            [self DownloadingMviFiles];
        }
        
    } else if(isJpgDownoad) {
        [self ReceiveJpgdata:theData];
    } else if(isHtmlDownload) {
        [self ReceiveHtmldata:theData];
    } else {
        NSLog(@"LastLineDidreceiveDAta");
    }
}
- (void )reTryDownload: (NSString *)reason {
    self.downloadManager.isDownloading = FALSE;
    if(appDelegate.isFullversionDownloading) {
        
        for (int i =1; i<3; i++) {
            
            NSString *strURL = arrFileDownloadList[intVideoCounter+i];
            NSString *sql = [NSString stringWithFormat:@"update FileDownloadList set isSkipFile = 1 where FileURL = '%@'",strURL];
            [[Database shareDatabase] Update:sql];
        }
        intVideoCounter = intVideoCounter+3;
        [self startFileDownloadingForIndex:intVideoCounter];
        
    } else if (appDelegate.isUpdateDownloading) {
        
        for (int i =1; i<3; i++) {
            
            NSString *strFileName = arrFileDownloadList[intVideoCounter+i][@"FileURL"];
            NSString *sql = [NSString stringWithFormat:@"update FileUpdateList set isSkipFile = 1, IsDownloaded = 0 where FileURL = '%@'",strFileName];
            
            [[Database shareDatabase] Update:sql];
        }
        intVideoCounter = intVideoCounter+3;
        [self startUpdateFileDownloadingForIndex:intVideoCounter];
    }
}
- (void)ReceiveJpgdata:(NSData*)theData {
    
    if ((self.mutArrFullVersion).count > intJpgFileCounter)
    {
        if (![theData writeToFile:(self.mutArrFullVersion)[intJpgFileCounter][@"savePath"] atomically:YES])
        {
            NSLog(@"File hasn't been saved.");
        }
        else
        {
            [self setAttribDonotMarkiCloudSetting:(self.mutArrFullVersion)[intJpgFileCounter][@"savePath"]];
            
        }
        
        lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intJpgFileCounter+intVideoCounter+intHtmlFileCounter+3,(self.mutArrFullVersion).count*3];
        
    }
    if(intJpgFileCounter==(self.mutArrFullVersion).count-1)
    {
        [self DownloadFullVersionMVI];
    }
    else {
        
        intJpgFileCounter++;
        [self downLoadimgImage];
    }
}
- (void)ReceiveHtmldata:(NSData*)theData
{
    if ((self.mutArrFullVersion).count > intHtmlFileCounter)
    {
        if (![theData writeToFile:(self.mutArrFullVersion)[intHtmlFileCounter][@"savePath"] atomically:YES])
            NSLog(@"File hasn't been saved.");
        else
            [self setAttribDonotMarkiCloudSetting:(self.mutArrFullVersion)[intHtmlFileCounter][@"savePath"]];
        
        
        
        lblNoofVideoCompleted.text = [NSString stringWithFormat:@"Downloading %d of %lu file(s)...",intVideoCounter+intHtmlFileCounter+2,(self.mutArrFullVersion).count*3];
    }
    if(intHtmlFileCounter==(self.mutArrFullVersion).count-1)
    {
        [self downLoadimgImage];
    }
    else
    {
        intHtmlFileCounter++;
        [self downLoadHTML];
    }
}
#pragma mark---
#pragma mark Downloading MVI files
-(void)DownloadingMviFiles
{
    isUpdatedJpgDownloading = FALSE;
    isMviDownloading = TRUE;
    if((self.mutArrTotalmviFiles).count>intMviCounter)
    {
        NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@/%@",DownloadFileURL,(self.mutArrTotalmviFiles)[intMviCounter][@"version"],(self.arrMviFile)[intMviCounter][@"mviFile"]];
        
        NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",Update_Path,(self.arrMviFile)[intMviCounter][@"mviFile"]];
        
        if ([[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            
            [[NSFileManager defaultManager] removeItemAtPath:strVideoPath error:nil];
        }
        
        if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath])
        {
            self.downloadManager.delegate = self;
            
            NSURL *url1 = [NSURL URLWithString:strVideoURL];
            [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
        }
        else
        {
            if(intMviCounter == (self.mutArrTotalmviFiles).count-1)
            {
                UpdateCompleted = TRUE;
                NSString *aStrSql = @"Update UpdatesList set UpdateDownloaded = '1'";
                [SharedDatabase Update:aStrSql];
                
                appDelegate.wasDownloadinProgress = FALSE;
                intParsingCounter = 1;
                intupdate=0;
                intUpdateCounter=inthtmlUpdateCounter=intJpgUpdateCounter=intMviCounter=0;
                intUpdatesdownloadCompleted = 1;
                
                lblNoofVideoCompleted.text = @"";
                lblfullorupdate.text = @"Updates downloaded";
                progressBar.hidden = YES;
                [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
                [UserDefaults setBool:YES forKey:@"UpdateDownloaded"];
                [UserDefaults synchronize];
                isUpdateDownloaded = TRUE;
                lblProgresspercent.text = @"Updates downloaded successfully";
                AppDelegateobj.isDownloadCancelled = false;
                [self startXMLParsingUpdated];
                [UIApplication sharedApplication].idleTimerDisabled = NO;
                
                if (userSubStat == InAppSubscribed)
                {
                    if ((appDelegate.mutArrDataItem).count > 0)
                    {
                        [self saveDBForInApp];
                    }
                }
            }
            else
            {
                intMviCounter++;
                if((self.mutArrTotalmviFiles).count>intMviCounter)
                {
                }
                [self DownloadingMviFiles];
            }
        }
    }
}

- (void)downloadFullVersionMVIAndSaveData {
    
    [self DownloadFullVersionMVI];
    dispatch_async(dispatch_get_main_queue(), ^{
        
        NSString *strSql = @"select count(UpdateUniqueId) from UpdatesList where UpdateDownloaded = 0";
        self->intupdate = [[Database shareDatabase] getCount:strSql];
        
        self->imgMenuUpdates.hidden = YES;
        self->lblMenuUpdates.hidden = YES;
        self->lblMenuUpdates.text = [NSString stringWithFormat:@"%d",self->intupdate];
        
        self->imgUpdatesAvailable.hidden = YES;
        self->lblUpdatesavailable.hidden  = YES;
        self->lblUpdatesavailable.text =[NSString stringWithFormat:@"%d",self->intupdate];
        
        //        if (AppDelegateobj.isUpdateDownloading) {
        // Remove alertview...
        [self dismissViewControllerAnimated:self->alertForUpdateMessage completion:nil];
        //        }
    });
}

- (void)DownloadFullVersionMVI {
    
    aStrMviFile = [UserDefaults  objectForKey:MVI_FILE_URL];
    
    // Version 3.0 change - Full doawnload crash issue
    // Make sure extension is ".mvi". ".mvi" is case sensative so .MVI will not work...
    NSArray *arrData = [aStrMviFile componentsSeparatedByString:@"."];
    aStrMviFile = [NSString stringWithFormat:@"%@.mvi", arrData.firstObject];
    
    FullorUpdate = 2;
    isHtmlDownload = FALSE;
    isJpgDownoad = FALSE;
    isMviDownloading = FALSE;
    isFullVersionXML = TRUE;
    NSString *strVideoURL = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,aStrMviFile];
    
#pragma mark --------------------------------
#pragma mark Version 2.4 Folder Structure Changes
    
    NSString *aStrDirPath = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:BITEFXV2];
    NSString *strVideoPath  = [NSString stringWithFormat:@"%@/%@", aStrDirPath, aStrMviFile];
    //    NSString *strVideoPath = [NSString stringWithFormat:@"%@%@",DEST_PATH,aStrMviFile];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath: strVideoPath]) {
        self.downloadManager.delegate = self;
        
        NSURL *url1 = [NSURL URLWithString:strVideoURL];
        [self.downloadManager download:strVideoURL path:strVideoPath withURL:url1];
        
    } else {
        
        [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
        [UserDefaults setBool:YES forKey:@"FullDownloaded"];
        [UserDefaults synchronize];
        
        [self startXMLParsing];
        isFullVersionXML = FALSE;
        
        //===================
        // Version 3.1 Changes
        //===================
        // Feature MVI file changes.
        //-----------------
        // Download Featues.MVI file if it is not downloaded previously due to any error or issue...
        [self downloadFeaturesMVI];
        //===================
        
        // To preserve User define sequences first we set FileTitle, now we can get back data using FileTitle. We need to update fileID because data is deleted and inserted again...
        if (isPreserveUserDefineSequences) {
            [self setUpdatedFileIdInUserDefineSequences];
        }
    }
    appDelegate.wasDownloadinProgress = FALSE;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        
        self->lblNoofVideoCompleted.text = @"";
        
        self->progressBar.hidden = YES;
        self->UpdateCompleted = TRUE;
        self->lblfullorupdate.text = @"Full-version download completed";
        self->intFullversiondownloadCompleted = 1;
        [[UIApplication sharedApplication]setNetworkActivityIndicatorVisible:FALSE];
        [UserDefaults setBool:YES forKey:@"FullDownloaded"];
        [UserDefaults synchronize];
        self->isFullVersionDownloaded = TRUE;
        
        NSMutableArray *aTempArray = [[Database shareDatabase]getAllDataForQuery:@"select * from FileDownloadList where isSkipFile = '1'"];
        NSString *aTempString;
        if(aTempArray.count == 0)
        {
            aTempString = defineFileDownloadSuccessMessage;
        }
        else
        {
            aTempString = [NSString stringWithFormat:@"All but %lu files were downloaded successfully",(unsigned long)aTempArray.count];
        }
        self->lblProgresspercent.text = aTempString;
        
        [UserDefaults  setBool:NO forKey:@"isDowmloadCancelled"];
        [UserDefaults  synchronize];
        
        AppDelegateobj.isDownloadCancelled = false;
        
        if (self->userSubStat == InAppSubscribed)
        {
            if ((appDelegate.mutArrDataItem).count > 0)
            {
                [self saveDBForInApp];
            }
        }
        [self onClickCheckForUpdate:nil];
        [UIApplication sharedApplication].idleTimerDisabled = NO;
        
    });
    
}

#pragma mark -

- (void)playVideoAfterLockingorRemoving:(id)sender {
    
    if(arrPlayerItems)
    {
        arrPlayerItems=nil;
    }
    arrPlayerItems = [[NSMutableArray alloc]init ];
    NSInteger total = (AppDelegateobj.mutArrPlayVideo).count;
    NSInteger aintSelectedVideoIndex = AppDelegateobj.intSelectedVideo;
    intcountofVideo=currentIndex=aintSelectedVideoIndex;
    
    //To set played flag True
    if([UserDefaults boolForKey:@"UpdateDownloaded"])
    {
        if([(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"thumbnailPath"]rangeOfString:@"/Documents/Update0001"].length > 0)
        {
            AppDelegateobj.isUpdateDownloading = TRUE;
            (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"isPlayed"] = @"1";
            
            NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isPlayed = '1' where  filePath=\'%@\'",(AppDelegateobj.mutArrPlayVideo)[currentIndex][@"filePath"]];
            [SharedDatabase Update:aStrUpdateSql];
        }
    }
    if(AppDelegateobj.mutArrPlayVideo)
    {
        for(int i=0;i<total;i++)
        {
            
            NSInteger lenght = [(AppDelegateobj.mutArrPlayVideo)[i][@"fileTitle"] length];
            
            NSString *firstVideoPath;
            NSString *aStrThumbImage = (AppDelegateobj.mutArrPlayVideo)[i][@"filePath"];
            
            NSString *strImageName = [(AppDelegateobj.mutArrPlayVideo)[i][@"filePath"] lastPathComponent];
            
            if([aStrThumbImage rangeOfString:@"Update0001"].length > 0) {
                
                NSString *dir_path = [NSString stringWithFormat:@"%@%@",Update_Path,strImageName];
                firstVideoPath = [dir_path stringByReplacingOccurrencesOfString:@"jpg" withString:VIDEO_EXTENSION];
                
            } else {
                
                if([aStrThumbImage rangeOfString:@"BiteFXiPadFull"].length > 0) {
                    
                    NSString *dir_path = [NSString stringWithFormat:@"%@%@",DEST_PATH,strImageName];
                    firstVideoPath = [dir_path stringByReplacingOccurrencesOfString:@"jpg" withString:VIDEO_EXTENSION];
                    
                } else {
                    
                    firstVideoPath = [NSString stringWithFormat:@"%@",[[NSBundle mainBundle]
                                                                       pathForResource:[(AppDelegateobj.mutArrPlayVideo)[i][@"fileTitle"] substringToIndex:lenght - 4] ofType:VIDEO_EXTENSION]];
                }
            }
            if (arrPlayerItems)
            {
                [arrPlayerItems addObject:firstVideoPath];//
            }
        }
    }
    @try {
        asset_ = [AVURLAsset URLAssetWithURL:[NSURL fileURLWithPath:arrPlayerItems[aintSelectedVideoIndex]] options:nil];
        [asset_ loadValuesAsynchronouslyForKeys:@[@"tracks"] completionHandler:^{
            
            // Get main Thread...
            dispatch_async(dispatch_get_main_queue(), ^{
                [self playVideoAtIndex:aintSelectedVideoIndex];
                [self.m_queueplayer pause];
            });
        }];
    }
    @catch (NSException *exception) {
        NSLog(@"Exception :%@",exception);
    }
    [self setPlayPauseImageForNormalState];
}
#pragma mark - In App Purchase Methods
- (void)inAppStart:(NSString*)aStrProductId ForPrice:(BOOL)aBoolForPrice {
    
    if (!aBoolForPrice) {
        
        //===================
        // Version 2.4 Changes
        //===================
        // Disable Price button untill purchase action is not completed...
        self.btnPurchase6Month.enabled = NO;
        self.btnPurchase1Year.enabled = NO;
    }
    
    inAppPurchase.productId = aStrProductId;
    //    [inAppPurchase requestForStore];
    // SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
    if ([aStrProductId isEqualToString:Str_ItemInApp1]) {
        inAppPurchase.tag = 1;
        inAppPurchase.product = self.product1Year;
        // SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
    } else if ([aStrProductId isEqualToString:Str_ItemInApp2]) {
        inAppPurchase.tag = 6;
        inAppPurchase.product = self.product6Month;
    }
    inAppPurchase.isGettingPrice = aBoolForPrice;
    [inAppPurchase startInAppPurchase];
}
- (void)subscribeProduct {
    //data={"email":"<EMAIL>","password":"indianic","inappitems":"MailBox,PicOfTheWeek,checkIn,photoalbum"}
    [UserDefaults  setObject:@"Purchased" forKey:@"check_in"];
    [UserDefaults  synchronize];
    // tag : 0
    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"In-App Successed" cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
}
- (void)callCompleteTransactoionAfterCompletion
{
    InAppPurchase *aInAppPurchase = [[InAppPurchase alloc] init];
    // SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
    if ([strInAppID isEqualToString:Str_ItemInApp1])
    {
        aInAppPurchase.tag = 1;
    }
    // SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
    else if ([strInAppID isEqualToString:Str_ItemInApp2])
    {
        aInAppPurchase.tag = 6;
    }
    //[aInAppPurchase completeTransactionAfterVarification:skPaymentTrasCurrent];
    if ((appDelegate.mutArrDataItem).count > 0)
    {
        [self saveDBForInApp];
    }
    else
    {
        //[appDelegate iCloudIntegrationforDB];
    }
}
#pragma mark -
#pragma mark InApp purchase Delegate
#pragma mark -
- (void) localPriceDidRecevied:(InAppPurchase *) inAppPurchase1 {
    // SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
    if ([inAppPurchase1.productId isEqual:Str_ItemInApp1] || inAppPurchase1.tag == 1)
    {
        str1YearPrice = [[NSString alloc] initWithFormat:@"%@",inAppPurchase1.localPrice];
        self.product1Year = inAppPurchase1.product;
        // SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
        [self inAppStart:Str_ItemInApp2 ForPrice:YES];
    }
    // SCM:2004-03-04 This option is no longer offered and will be removed from the code (per Doug). We only offer montly autorenewal subscriptions.
    else if ([inAppPurchase1.productId isEqual:Str_ItemInApp2] || inAppPurchase1.tag == 6)
    {
        str6MonthsPrice = [[NSString alloc] initWithFormat:@"%@",inAppPurchase1.localPrice];
        self.product6Month = inAppPurchase1.product;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [self->tblViewPurchaseOption reloadData];
    });
    
}
- (void) purchaseDidFinish:(InAppPurchase *) inAppPurchase {
    // tag : 0
    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:@"In-App Successed" cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    
}
- (void) purchaseDidFail:(InAppPurchase *) inAppPurchase {
    [appDelegate stopIndicator];
    
    //===================
    // Version 2.4 Changes
    //===================
    // Disable Price button untill purchase action is not completed...
    self.btnPurchase6Month.enabled = YES;
    self.btnPurchase1Year.enabled = YES;
}
-(void) restoreDidFinish:(NSString  *) inAppPurchase{
    [appDelegate stopIndicator];
    [self inAppStart:inAppPurchase ForPrice:NO];
}
- (void) localPriceDidFail:(InAppPurchase *) inAppPurchase {
}
- (void)callBeforeCompeleTransaction:(SKPaymentTransaction *)atransaction {
    
    skPaymentTrasCurrent = atransaction;
    
    if(AppDelegateobj.connected) {
        [appDelegate stopIndicator];
        [self callWSforInAppVarification];
    } else {
        // tag : 0
        [UIAlertController showAlertInViewController:self withTitle:ErrorTitle message:ErrorMsg cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
    }
    //    userSubStat = InAppSubscribed;
    //    [UserDefaults  setInteger:InAppSubscribed forKey:@"UserStatusSubscription"];
    //    [UserDefaults synchronize];
}
#pragma mark - PurchaseOptionCustomCell Delegate

- (void)clickedPurchase:(NSInteger)aIntTag {
    
    //===================
    // Version 3.0 Changes
    //===================
    // New InApp Code changes...
    [appDelegate startPurchaseIndicatorWithID:aIntTag];
    
    // Disable Price button untill purchase action is not completed...
    self.btnPurchase1Month.enabled = NO;
    
    [[IAPManager sharedInstance] buy:self.product1Month completion:^(BOOL success, SKPaymentTransaction * _Nonnull transaction) {
        
        [appDelegate stopIndicator];
        self.btnPurchase1Month.enabled = YES;
        if (success) {
            // Call API...
            [self callBeforeCompeleTransaction:transaction];
        }
        else
        {
            // Show Error message...
            NSString *aStrMessage = TRANSACTION_FAIL_ERROR;
            if (transaction.error) {
                aStrMessage = transaction.error.localizedDescription;
            }
            
            if (transaction.error.code != SKErrorPaymentCancelled) {
                
                // Show error message...
                [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrMessage cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
            }
        }
        
    }];
}
- (IBAction)onClickPurchaseClose:(id)sender
{
    if(viewPurchase)
    {
        [viewPurchase removeFromSuperview];
    }
}
- (void)moreClicked
{
    [self showSubscriptionHtmls:@"BiteFXiPad-Subscription"];
}

- (IBAction)btnTermsnPrivacyClick:(id)sender {
    
    UIButton *btnSender = (UIButton *)sender;
    [self.view addSubview:viewPurchase];
    viewPurchaseDetails.frame = CGRectMake(91, 100, 748, 545);
    webViewPurchaseDetails.frame = CGRectMake(0, 0, viewPurchaseDetails.frame.size.width, viewPurchaseDetails.frame.size.height);
    [self termsClicked:btnSender.tag];
    if (btnSender.tag == 1) {
        lblPurchaseOptions.text = @"Terms and Conditions";
    } else if (btnSender.tag == 2) {
        lblPurchaseOptions.text = @"Privacy Policy";
    } else {
        lblPurchaseOptions.text = @"Support";
    }
    btnProducts.hidden = YES;
}

- (void)termsClicked:(NSInteger)aIntTag {
    if (aIntTag == 1) {
        [self showSubscriptionHtmls:@"BiteFXiPad-Terms-and-Conditions"];
    } else if (aIntTag == 2) {
        [self showSubscriptionHtmls:@"BiteFXiPad-Privacy"];
    } else {
        [self showSubscriptionHtmls:@"BiteFXiPad-Support"];
    }
}

- (void)showSubscriptionHtmls:(NSString *)strFileName {
    //    [webViewPurchaseDetails loadHTMLString:@"" baseURL:[NSURL URLWithString:@""]];
    [webViewPurchaseDetails loadRequest:[NSURLRequest requestWithURL:[NSURL fileURLWithPath:[[NSBundle mainBundle] pathForResource:strFileName ofType:@"html"]]]];
    
    tblViewPurchaseOption.hidden = YES;
    webViewPurchaseDetails.hidden = NO;
    viewPurchaseDetails.hidden = NO;
    btnProducts.hidden = NO;
}

- (IBAction)onClickProducts:(id)sender
{
    if (tblViewPurchaseOption.hidden)
    {
        webViewPurchaseDetails.hidden = YES;
        viewPurchaseDetails.hidden = YES;
        tblViewPurchaseOption.hidden = NO;
        btnProducts.hidden = YES;
        btnRestore.hidden = NO;
        
        //        if ([UserDefaults  boolForKey:@"Restore"])
        //            btnRestore.enabled = YES;
        //        else
        //            btnRestore.enabled = NO;
    }
}
- (IBAction)onClickRestore:(id)sender {
    
    if(![appDelegate isNetWorkAvailable]) {
        [self NoInternetConnectionShowAlert];
    } else {
        [appDelegate startIndicator];
        //        [inAppPurchase restorePurchase];
        
        //===================
        // Version 3.0 Changes
        //===================
        // New InApp Code changes...
        
        // Disable Price button untill purchase action is not completed...
        self.btnPurchase1Month.enabled = NO;
        
        [[IAPManager sharedInstance] restorePurchase:^(BOOL success, SKPaymentTransaction * _Nonnull transaction) {
            
            [appDelegate stopIndicator];
            self.btnPurchase1Month.enabled = YES;
            if (success) {
                // Call API...
                [self callBeforeCompeleTransaction:transaction];
            }
            else
            {
                // Show Error message...
                NSString *aStrMessage = RESTORE_FAIL_ERROR;
                if (transaction.error) {
                    aStrMessage = transaction.error.localizedDescription;
                }
                
                if (transaction.error.code != SKErrorPaymentCancelled) {
                    
                    // Show error message...
                    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrMessage cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                }
            }
            
        }];
        
    }
}

-(IBAction)btnManageSubscription:(id)sender {
    //    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:MANAGE_SUBSCRPTION_URL] options:@{} completionHandler:^(BOOL success) {
        if (success) {
            NSLog(@"Opened Manage Subscription url");
        }
    }];
}


#pragma mark -Do not backup AttributeSet
-(void)setAttribDonotMarkiCloudSetting:(NSString *)str
{
    NSURL *aurlHtml = [NSURL fileURLWithPath:str];
    BOOL attribSet = [appDelegate addSkipBackupAttributeToItemAtURL:aurlHtml];
    if(attribSet)
    {
        NSLog(@"Done");
    }
}
#pragma mark - Get Days between two dates
- (NSInteger) daysToDate:(NSString *) endDate
{
    NSDateFormatter *aDtfrmt = [[NSDateFormatter alloc] init];
    aDtfrmt.dateFormat = @"dd/MM/yyyy";
    NSDate *endDt =  [aDtfrmt dateFromString:endDate];
    
    NSDate *aDtCurr;
    
    if (strCurrentDate)
    {
        aDtCurr = [aDtfrmt dateFromString:strCurrentDate];
    }
    else
    {
        aDtCurr = [NSDate date];
    }
    NSTimeInterval atimeCurrent = aDtCurr.timeIntervalSince1970;
    NSTimeInterval atimeEndDate = endDt.timeIntervalSince1970;
    
    NSInteger days = -1;
    
    if (atimeEndDate >= atimeCurrent)
    {
        unsigned int unitFlags = NSCalendarUnitDay;
        NSCalendar *gregorian = [[NSCalendar alloc]
                                 initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
        NSDateComponents *comps = [gregorian components:unitFlags fromDate:aDtCurr  toDate:endDt options:0];
        days = comps.day;
    }
    return days;
}
- (NSString *)strGetDate :(NSString*)aStrDate{
    NSDateFormatter *aDateFormatter = [[NSDateFormatter alloc] init];
    aDateFormatter.dateFormat = @"YYYYmmDD";
    
    NSDate *date = [aDateFormatter dateFromString:aStrDate];
    
    NSDateFormatter *aDateFormatter2 = [[NSDateFormatter alloc] init];
    aDateFormatter2.dateFormat = @"dd/mm/yyyy";
    NSString *aNewDateFormate = [aDateFormatter2 stringFromDate:date];
    
    return aNewDateFormate;
}
- (void)checkForAvaibility
{
    NSString *aStrNameQuery = [NSString stringWithFormat:@"select * from InAppTbl"];
    NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
    
    if (aMutArrNames.count > 0)
    {
        NSString *aStrEndDate = aMutArrNames[0][@"End_Date"];
        NSString *aStrKind = aMutArrNames[0][@"Kind"];
        
        BOOL aBoolKind;
        if ([aStrKind isEqualToString:@"InApp"])
        {
            aBoolKind = NO;
        }
        else
        {
            aStrEndDate = [self dateFromDate:aStrEndDate afterNumberOfDays:30 isAfter:YES];
            aBoolKind = YES;
        }
        NSInteger aIntNumOfDays = [self daysToDate:aStrEndDate];
        if (aIntNumOfDays >= 0)
        {
            if (aBoolKind)
            {
                if (aIntNumOfDays < 31 && aIntNumOfDays != 0)
                {
                    NSString *aStrMSG = [NSString stringWithFormat:@"Your BiteFX Subscription has expired. This may be because your credit card details have changed or because there was a problem charging your credit card. Please call BiteFX at 1-877-224-8339 (+1-530-582-1189) to renew your subscription or use the Purchase option to purchase an iPad-only subscription.BiteFX will revert to its basic (free) form in %ld days.",(long)aIntNumOfDays];
                    
                    // tag 232
                    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrMSG cancelButtonTitle:@"Close" destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                        [self dismissViewControllerAnimated:YES completion:nil];
                        if(buttonIndex == controller.cancelButtonIndex)
                        {
                            [self RegisterAction:nil];
                        }
                    }];
                }
                else if (aIntNumOfDays == 0)
                {
                    // remove data from database.
                    [self moveToBasicVersion];
                }
            }
            else
            {
                if (aIntNumOfDays < 11 && aIntNumOfDays != 0)
                {
                    NSString *aStrMSG = [NSString stringWithFormat:@"Your BiteFX subscription expires on %@. \nTo continue using the full BiteFX content you need to renew your subscription before %@ by purchasing another subscription period (unfortunately at this time, Apple does not allow us to use their auto-renewing subscription)",aStrEndDate,aStrEndDate];
                    
                    // tag 233
                    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aStrMSG cancelButtonTitle:@"Purchase Later" destructiveButtonTitle:nil otherButtonTitles:@[@"Purchase Now"] tapBlock:^(UIAlertController * _Nonnull controller, UIAlertAction * _Nonnull action, NSInteger buttonIndex) {
                        [self dismissViewControllerAnimated:YES completion:nil];
                        if(buttonIndex == controller.firstOtherButtonIndex)
                        {
                            [self PurchaseAction:nil];
                        }
                    }];
                }
                else if (aIntNumOfDays == 0)
                {
                    // remove data from database.
                    [self moveToBasicVersion];
                }
            }
        }
    }
}
- (NSString *)dateFromDate:(NSString *)date afterNumberOfDays:(int)aInt  isAfter:(BOOL)aBoolIsAfter{
    NSDateFormatter *aDtfrmt = [[NSDateFormatter alloc] init];
    aDtfrmt.dateFormat = @"dd/MM/yyyy";
    NSTimeInterval time = [aDtfrmt dateFromString:date].timeIntervalSince1970;
    
    // for 6 months
    double dble = 60 * 60 * 24 * aInt;
    if (aBoolIsAfter)
    {
        time = time + dble;
    }
    else
    {
        time = time - dble;
    }
    NSDate *newDt = [[NSDate alloc] initWithTimeIntervalSince1970:time];
    NSString *aStrResult = [aDtfrmt stringFromDate:newDt];
    return aStrResult;
}

- (NSDate *)dateFromDate:(NSDate *)date afterNumberOfDays:(int)aInt{
    NSTimeInterval time = date.timeIntervalSince1970;
    // for 6 months
    double dble = 60 * 60 * 24 * aInt;
    time = time + dble;
    NSDate *newDt = [[NSDate alloc] initWithTimeIntervalSince1970:time];
    return newDt;
}

#pragma mark - iCloud Back Up Methods
- (void)saveDBForInApp{
    if (appDelegate.mutArrDataItem.count > 0){
        ICloud *cloud = [ICloud shareCloud];
        [cloud saveFile:(appDelegate.mutArrDataItem)[0] withData:nil];
    }
}
#pragma mark - Move to basic version method
- (void)moveToBasicVersion
{
    intupdate = 0;
    imgMenuUpdates.hidden = YES;
    imgUpdatesAvailable.hidden = YES;
    lblMenuUpdates.hidden = YES;
    
    btnRemainDownload.hidden = YES;
    isUpdateDownloaded = FALSE;
    intVideoCounter=intHtmlFileCounter=intJpgFileCounter=0;
    // isRegistrationSuccess = FALSE;
    isRegistered=FALSE;
    intUnregisterDone = 1;
    intUpdateCounter=intJpgUpdateCounter=inthtmlUpdateCounter=intMviCounter=0;
    
    [UserDefaults setBool:NO forKey:@"UpdateDownloaded"];
    [UserDefaults setBool:NO forKey:@"FullDownloaded"];
    [UserDefaults setBool:NO forKey:@"Registered"];
    [UserDefaults synchronize];
    
    self.txtFieldEmail.enabled = TRUE;
    txtFieldSerialnumber1.enabled = TRUE;
    
    [SharedDatabase Delete:@"DELETE FROM LocalFileMaster"];
    [SharedDatabase Delete:@"DELETE FROM CollectionMaster"];
    [SharedDatabase Delete:@"DELETE FROM UpdateMaster"];
    [SharedDatabase Delete:@"DELETE FROM CollectionFilesMaster"];
    [SharedDatabase Delete:@"DELETE FROM UpdatesList"];
    [SharedDatabase Delete:@"DELETE FROM FileDownloadList"];
    [SharedDatabase Delete:@"DELETE FROM FileUpdateList"];
    [SharedDatabase Delete:@"DELETE FROM UpdateFileMaster"];
    
    [self startXMLParsing];
    [self insertPlayListCollection];
    [self getFirstCollectionVideoDetail];
    [self playVideoAtIndex:0];
    [self.m_queueplayer pause];
    [self setPlayPauseImageForNormalState];
    
    appDelegate.intSelectedScrNo = 1;
    appDelegate.intSelectedVideo  = 0;
    //Change to remove updates from the document-directory.
    [self RemoveFromDocumentDirectory];
    
    userSubStat = NotSubscribed;
    
    [UserDefaults  setInteger:NotSubscribed forKey:@"UserStatusSubscription"];
    [UserDefaults  synchronize];
}
//Called when Internet Connection status is changed
- (void) updateInterfaceWithReachability: (Reachability*) curReach {
    
    if(curReach == internetReach || curReach == hostReach || curReach == wifiReach) {
        
        if(![appDelegate isNetWorkAvailable]) {
            
            if(self.downloadManager.isDownloadFailed == 1)
            {
                self.downloadManager.isDownloadFailed = 2;
                if(viewDownloadingData)
                {
                    [viewDownloadingData removeFromSuperview];
                }
            }
        } else {
            if (self.downloadManager.isDownloadFailed == 2){
                self.downloadManager.isDownloadFailed = 0;
                //[self checkForRemainDownload];
                [self checkForRemainDownloadOnNetwork];
            }
        }
    }
}
//Called by Reachability whenever status changes
- (void) reachabilityChanged: (NSNotification* )note {
    
    Reachability* curReach = note.object;
    NSParameterAssert([curReach isKindOfClass: [Reachability class]]);
    [self updateInterfaceWithReachability: curReach];
}
//Custom methods will created by tarun start....
-(void)removeviewPopupFromSuperView
{
    if(viewPopup!=nil)
    {
        [viewPopup removeFromSuperview];
    }
}

-(void)NoInternetConnectionShowAlert{
    if(![appDelegate isNetWorkAvailable]) {
        // tag : 0
        [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:definenoInternetConnection cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
        return;
    }
}
-(void) setPlayPauseImageForNormalState{
    [playPauseBtn setImage:[UIImage imageNamed:@"PlayButtonImg.png"] forState:UIControlStateNormal];
}
-(void)setSliderProperty{
    UIImage *athumbImage = [UIImage imageNamed:@"SliderKnob.png"];
    UIImage *athumbImageselected = [UIImage imageNamed:@"SliderKnobPressed.png"];
    UIImage *sliderMinTrackImage = [UIImage imageNamed: @"SliderFilled.png"];
    UIImage *sliderMaxTrackImage = [UIImage imageNamed: @"SliderArea.png"];
    
    sliderMinTrackImage = [sliderMinTrackImage resizableImageWithCapInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    sliderMaxTrackImage = [sliderMaxTrackImage resizableImageWithCapInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    
    [sliderTimeForVideo setMinimumTrackImage:sliderMinTrackImage forState:UIControlStateNormal];
    [sliderTimeForVideo setMaximumTrackImage:sliderMaxTrackImage forState:UIControlStateNormal];
    sliderTimeForVideo.minimumTrackTintColor = [UIColor colorWithRed:0.467 green:0.482 blue:0.502 alpha:1.000];
    [sliderTimeForVideo setThumbImage:athumbImage forState:UIControlStateNormal];
    [sliderTimeForVideo setThumbImage:athumbImageselected forState:UIControlStateHighlighted];
}
-(void)LoadWbViewWithURL
{
    NSString *strURL = [self getHtmlString:nil];
    [wbView loadRequestWithURL:strURL];
}
-(void)SetTimerframeConterInvalidate
{
    if(timerFrameCounter!=nil) {
        [timerFrameCounter invalidate];
        timerFrameCounter = nil;
    }
}
-(void)SetPlayerToNormalStateWithPause
{
    isPlaying = FALSE;
    [self setPlayPauseImageForNormalState];
    [self.m_queueplayer pause];
}

-(void)manageAlignmentTitleLabel
{
    if([self.view.subviews containsObject:wbView] || appDelegate.isPresentationInfoOpen)
    {
        lbl_view_title.frame = CGRectMake(324, 0 , 605 , 53);
        //        NSLog(@"Manage Align If part");
    }
    else
    {
        lbl_view_title.frame = CGRectMake(0, 0 , 929 , 53);
        //        NSLog(@"Manage Align Else part");
    }
}
-(void)displayFileTitle
{
    [self manageAlignmentTitleLabel];
    lbl_view_title.alpha = 1.0;
    lbl_view_title.text = appDelegate.selectedObj != nil ? (appDelegate.arrMedia)[0][@"fileName"] : (AppDelegateobj.mutArrPlayVideo)[currentIndex][@"fileName"];
    NSLog(@"===============/n/ntitle text : %@\n=============================",lbl_view_title.text);
}
-(void)HideFileTitle
{
    [UIView animateWithDuration:1
                          delay:3
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        [self manageAlignmentTitleLabel];
        self->lbl_view_title.alpha = 0.0;
    } completion:^(BOOL finished) {
    }];
}
-(void)showHideFileTitleWithAnimation
{
    [self displayFileTitle];
    [UIView animateWithDuration:0.50 animations:^{
        
    } completion:^(BOOL finished){
        [self HideFileTitle];
    }];
}
-(void)setMenuButtonNormal
{
    if(btnMenu.selected)
        btnMenu.selected=NO;
}
-(void)setHelpButtonNormal
{
    if(btnHelp.selected)
        btnHelp.selected=NO;
}

#pragma mark --------------------------------
#pragma mark Version 2.4 Changes
- (void)checkVersionAndDownloadNewData {
    
    int aIntNewDataDownloaded  = [UserDefaults boolForKey:IS_NEW_DATA_DOWNLOADED_FOR_VERSION_24];
    
    if (!aIntNewDataDownloaded) {
        
        // First delete old data...
        [SharedDatabase Delete:@"DELETE FROM FileDownloadList"];
        [SharedDatabase Delete:@"DELETE FROM UpdateMaster"];
        [SharedDatabase Delete:@"DELETE FROM UpdatesList"];
        [SharedDatabase Delete:@"DELETE FROM FileUpdateList"];
        [SharedDatabase Delete:@"DELETE FROM UpdateFileMaster"];
        
        [UserDefaults setBool:NO forKey:@"UpdateDownloaded"];
        [UserDefaults setBool:NO forKey:@"FullDownloaded"];
        
        // Get loggedin user data...
        NSString *aStrNameQuery = [NSString stringWithFormat:selectAllDataFromUserTable];
        NSMutableArray *aMutArrNames = [SharedDatabase getAllDataForQuery:aStrNameQuery];
        
        self.strEmail = aMutArrNames[0][@"emailId"];
        self.strSerialNumber = aMutArrNames[0][@"SerialNumber"];
        
        // Now call "login3" API to get new data...
        NSMutableDictionary *body = [[NSMutableDictionary alloc]init];
        
        body[@"method"] = LOGIN;
        body[@"emailID"] = self.strEmail;
        body[@"password"] = self.strSerialNumber;
        body[@"udid"] = UDIDForWS;
        
        isNewVersionDataDownloading = YES;
        //        CallWebService *webservice;
        //
        //        webservice =[[CallWebService alloc] initWithURL:WebserviceURLuserTesting delegate:self args:body key:@"RegisterUser"];
        
        WebService *aWebService = [[WebService alloc] init];
        [aWebService initWithPostRequest:body controller:self callSilently:YES completion:^(id object, NSError* aError) {
            
            if (aError) {
                // Show error message...
                [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:aError.localizedDescription cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
                self->btnRegister.enabled = YES;
                return;
            }
            NSDictionary *aDictResponse = object;
            [self registerUser:aDictResponse];
        }];
        
        
    }
    
}

- (void)removeOldVersionDataFromDocumentDirectory {
    
    NSString *aStrFullPath = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:@"/BiteFXiPadFull"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isExists = [fileManager fileExistsAtPath:aStrFullPath];
    
    if(isExists)
    {
        [fileManager removeItemAtPath:aStrFullPath error:nil];
    }
    
}

#pragma mark --------------------------------
#pragma mark Preserve User define sequences

// Methods to preserve User define sequence for version 2.3 to 2.4 change and Basic version to FullDownload...
- (void)setFileTitleInUserDefineSequences {
    
    // First check for column "fileTitle" in CollectionUserDefineFiles...
    NSString *aStrSql = [NSString stringWithFormat:@"select fileTitle from CollectionUserDefineFiles"];
    BOOL aBoolColumnExists = [[Database sharedDatabase] checkColumnExists:aStrSql];
    
    if (!aBoolColumnExists)
    {
        NSString *aStrUpdateQuery = [NSString stringWithFormat:@"ALTER TABLE CollectionUserDefineFiles ADD COLUMN fileTitle TEXT DEFAULT ''"];
        [[Database sharedDatabase] Update:aStrUpdateQuery];
    }
    
    isPreserveUserDefineSequences = YES;
    
    // Now get all filesID from CollectionUserDefineFiles...
    NSString *aStrQuery = [NSString stringWithFormat:@"select Distinct filesID from CollectionUserDefineFiles Order By filesID"];
    
    NSMutableArray *aMutArrFilesId = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
    for (int aIntJ = 0; aIntJ < aMutArrFilesId.count; aIntJ++)
    {
        NSString *aStrFileId = aMutArrFilesId[aIntJ][@"filesID"];
        NSString *aStrFileQuery = [NSString stringWithFormat:@"select fileTitle from LocalFileMaster where fileID = \'%@\'", aStrFileId];
        
        NSMutableArray *aMutArrFileTitles = [[Database sharedDatabase] getAllDataForQuery:aStrFileQuery];
        
        if (aMutArrFileTitles.count > 0)
        {
            // Set "fileTitle" in CollectionUserDefineFiles...
            NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update CollectionUserDefineFiles set fileTitle = \'%@\' where filesID  =\'%@\'", aMutArrFileTitles[0][@"fileTitle"], aStrFileId];
            [SharedDatabase Update:aStrUpdateSql];
            
        }
    }
    
}

- (void)setUpdatedFileIdInUserDefineSequences {
    
    isPreserveUserDefineSequences = NO;
    
    // First get all fileTitle from CollectionUserDefineFiles...
    NSString *aStrQuery = [NSString stringWithFormat:@"select Distinct fileTitle from CollectionUserDefineFiles Order By filesID"];
    
    NSMutableArray *aMutArrFileTitles = [[Database sharedDatabase] getAllDataForQuery:aStrQuery];
    for (int aIntJ = 0; aIntJ < aMutArrFileTitles.count; aIntJ++)
    {
        NSString *aStrFileTitle = aMutArrFileTitles[aIntJ][@"fileTitle"];
        NSString *aStrFileQuery = [NSString stringWithFormat:@"select fileID from LocalFileMaster where fileTitle = \'%@\'", aStrFileTitle];
        
        NSMutableArray *aMutArrFileIds = [[Database sharedDatabase] getAllDataForQuery:aStrFileQuery];
        
        if (aMutArrFileIds.count > 0)
        {
            // We find file for fileTitle...
            // Set "filesID" in CollectionUserDefineFiles...
            NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update CollectionUserDefineFiles set filesID = \'%@\' where fileTitle =\'%@\'", aMutArrFileIds[0][@"fileID"], aStrFileTitle];
            [SharedDatabase Update:aStrUpdateSql];
        }
        else
        {
            // We don't find file for fileTitle...
            // So we delete that entry from CollectionUserDefineFiles...
            NSString *aStrDeleteSql = [NSString stringWithFormat:@"DELETE FROM CollectionUserDefineFiles where fileTitle= \'%@\'",aStrFileTitle];
            [SharedDatabase Delete:aStrDeleteSql];
            aStrDeleteSql = nil;
        }
    }
    
}

#pragma mark --------------------------------
#pragma mark Version 2.5 change Delete Updates when app version is changed.

- (void)deleteOldUpdatesDataForNewAppVersion {
    
    int aIntOldDataDeleted = [UserDefaults boolForKey:IS_OLD_UPDATES_DATA_DELETED_FOR_VERSION_25];
    
    if (!aIntOldDataDeleted) {
        
        // Delete old data...
        // No need to preserve the updates list from one version to the next.
        [SharedDatabase Delete:@"DELETE FROM UpdateMaster"];
        [SharedDatabase Delete:@"DELETE FROM UpdatesList"];
        [SharedDatabase Delete:@"DELETE FROM FileUpdateList"];
        [SharedDatabase Delete:@"DELETE FROM UpdateFileMaster"];
        
        [UserDefaults setBool:NO forKey:@"UpdateDownloaded"];
        
        [UserDefaults setBool:YES forKey:IS_OLD_UPDATES_DATA_DELETED_FOR_VERSION_25];
    }
    
}

//Custom methods will created by tarun end....

- (void)newUpdatedXMLParser {
    
    NSData *xmldata=[NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"BiteFxUpdate" ofType:@"MVI"]];
    NSString *myString;
    myString = [[NSString alloc] initWithData:xmldata encoding:NSASCIIStringEncoding];
    
    NSLog(@"Data to String ==== %@",myString);
    
    NSXMLParser *xmlParser = [[NSXMLParser alloc] initWithData:xmldata];
    
    //Initialize the delegate.
    XMLParserUpdate *parser = [[XMLParserUpdate alloc] initXMLParserUpdate];
    
    //Set delegate
    xmlParser.delegate = parser;
    
    //Start parsing the XML file.
    BOOL successParse = [xmlParser parse];
    if(successParse){
        NSLog(@"No Errors");
    } else {
        NSLog(@"startXMLParsing Error Error Error!!!");
    }
    
}

- (void)newFeatureXMLParser {
    
    NSData *xmldata=[NSData dataWithContentsOfFile:[[NSBundle mainBundle]pathForResource:@"Features" ofType:@"MVI"]];
    NSString *myString;
    myString = [[NSString alloc] initWithData:xmldata encoding:NSASCIIStringEncoding];
    
    NSLog(@"Data to String ==== %@",myString);
    
    NSXMLParser *xmlParser = [[NSXMLParser alloc] initWithData:xmldata];
    
    //Initialize the delegate.
    XMLParserFeature *parser = [[XMLParserFeature alloc] initXMLParserFeature];
    
    //Set delegate
    xmlParser.delegate = parser;
    
    //Start parsing the XML file.
    BOOL successParse = [xmlParser parse];
    if(successParse){
        NSLog(@"No Errors");
    } else {
        NSLog(@"startXMLParsing Feature Error Error Error!!!");
    }
    
}

-(void) refreshReceipt {
    SKReceiptRefreshRequest *req = [[SKReceiptRefreshRequest alloc] initWithReceiptProperties:nil];
    req.delegate = self;
    [req start];
}

- (void)requestDidFinish:(SKRequest *)request {
    if([request isKindOfClass: [SKReceiptRefreshRequest class]]) {
        [SVProgressHUD dismiss];
        [self PurchaseAction:nil];
    }
}

- (void)request:(SKRequest *)request didFailWithError:(NSError *)error {
    [SVProgressHUD dismiss];
    [UIAlertController showAlertInViewController:self withTitle:strAlertTitle message:REFRESH_RECEIPT_ERROR cancelButtonTitle:strAlertCancelTitle destructiveButtonTitle:nil otherButtonTitles:nil tapBlock:nil];
}

- (NSAttributedString *)attributedStringForBulletTexts:(NSArray *)stringList
                                              withFont:(UIFont *)font
                                          bulletString:(NSString *)bullet
                                           indentation:(CGFloat)indentation
                                           lineSpacing:(CGFloat)lineSpacing
                                      paragraphSpacing:(CGFloat)paragraphSpacing
                                             textColor:(UIColor *)textColor
                                           bulletColor:(UIColor *)bulletColor {
    
    NSDictionary *textAttributes = @{NSFontAttributeName: font,
                                     NSForegroundColorAttributeName: textColor};
    NSDictionary *bulletAttributes = @{NSFontAttributeName: font, NSForegroundColorAttributeName: bulletColor};
    
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    paragraphStyle.tabStops = @[[[NSTextTab alloc] initWithTextAlignment: NSTextAlignmentLeft location:indentation options:@{}]];
    paragraphStyle.defaultTabInterval = indentation;
    paragraphStyle.lineSpacing = lineSpacing;
    paragraphStyle.paragraphSpacing = paragraphSpacing;
    paragraphStyle.headIndent = indentation;
    
    NSMutableAttributedString *bulletList = [NSMutableAttributedString new];
    
    for (NSString *string in stringList) {
        NSString *formattedString = [NSString stringWithFormat:@"%@\t%@\n", bullet, string];
        NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:formattedString];
        if (string == stringList.lastObject) {
            paragraphStyle = [paragraphStyle mutableCopy];
            paragraphStyle.paragraphSpacing = 0;
        }
        [attributedString addAttributes:@{NSParagraphStyleAttributeName: paragraphStyle} range:NSMakeRange(0, attributedString.length)];
        [attributedString addAttributes:textAttributes range:NSMakeRange(0, attributedString.length)];
        
        NSRange rangeForBullet = [formattedString rangeOfString:bullet];
        [attributedString addAttributes:bulletAttributes range:rangeForBullet];
        [bulletList appendAttributedString:attributedString];
    }
    
    return bulletList;
}

//===================
// Version 3.1 Changes
//===================
// Feature MVI file changes.

- (void)downloadFeaturesMVI {
    
    NSString *aStrFeaturesMviFile = [UserDefaults objectForKey:FEATURES_MVI_FILE_URL];
    NSString *aStrDirPath = [DOCUMENT_DIRECTORY_PATH stringByAppendingPathComponent:BITEFXV2];
    NSString *aStrFilePath  = [NSString stringWithFormat:@"%@/%@", aStrDirPath, aStrFeaturesMviFile];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath: aStrFilePath]) {
        
        // Download the file in a seperate thread.
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            NSLog(@"Downloading Started");
            NSString *aStrUrlToDownload = [NSString stringWithFormat:@"%@/%@",DownloadFileURL,aStrFeaturesMviFile];
            NSURL *aURL = [NSURL URLWithString:aStrUrlToDownload];
            NSData *aData = [NSData dataWithContentsOfURL:aURL];
            if ( aData ) {
                NSString *aStrFilePath  = [NSString stringWithFormat:@"%@/%@", aStrDirPath, aStrFeaturesMviFile];
                // Saving is done on main thread
                dispatch_async(dispatch_get_main_queue(), ^{
                    if( [aData writeToFile:aStrFilePath atomically:YES]) {
                        [self setAttribDonotMarkiCloudSetting:aStrFilePath];
                        
                        // After file downloading do FeatureTags XMLParsing...
                        [self startXMLParsingFeatureSet];
                        
                    } else {
                        //NSLog(@"file can not save for %@",strURL);
                    }
                });
            }
            
        });
        
    }
    
    // Do FeatureTags XMLParsing...
    [self startXMLParsingFeatureSet];
    
}

#pragma mark - Hide Image & Video from full screen
-(void)btnCrossClick:(UIButton *)sender {
    
    if(intFullversiondownloadCompleted||intUnregisterDone) {
        animation = nil;
        animation.videoPlayDelegate = nil;
    }
    
    if(AppDelegateobj.isUpdateDownloading) {
        animation = nil;
        animation.videoPlayDelegate = nil;
    }
    
    animation = [[AnimationPanelVC alloc]initWithNibName:@"AnimationPanelVC" bundle:nil];
    animation.videoPlayDelegate = self;
    //        }
    sliderSpeedControl.enabled = NO;
    loopBtn.enabled = NO;
    playPauseBtn.enabled = NO;
    
    //Create Animation Pane - Scrollview
    animation.view.frame = CGRectMake(0, 0, 929, 748);
    (animation.view).backgroundColor = [UIColor darkTextColor];
    verticalToolbarView.frame  = CGRectMake(929, 0, 95, 696);
    [self.view addSubview:animation.view];
        
    [btnAnimationPanel setSelected:YES];
    [btnInfo setEnabled:NO];
    [btnPreviousFrame setEnabled:NO];
    [btnNextFrame setEnabled:NO];
    [btnMenu setEnabled:YES];
    btnRemainDownload.hidden = YES;
    btn_SpeedControl.alpha = 0.5f;
}

#pragma mark - Fav/UnFav Image
-(void)btnFavClick:(UIButton *)sender {
    NSString *filesID = [NSString stringWithFormat:@"%@", appDelegate.selectedObj != nil ? (appDelegate.arrScrollViewCount)[0][@"filesID"] : (appDelegate.mutArrPlayVideo)[currentIndex][@"filesID"]];
    NSLog(@"filesID:::%@", filesID);
    
    //===================
    // Make fav or unfav data to LocalFileMaster...
    //===================
    NSString *aStrUpdateSql = [NSString stringWithFormat:@"Update LocalFileMaster set isFavourite = %d where fileID = %@",sender.isSelected ? 0 : 1, filesID];
    [[Database sharedDatabase]Update:aStrUpdateSql];
    
    NSString *favourite = [NSString stringWithFormat:@"%@", appDelegate.selectedObj != nil ? (appDelegate.arrScrollViewCount)[0][@"isFavourite"] : (appDelegate.mutArrPlayVideo)[currentIndex][@"isFavourite"]];
    
    NSLog(@"favourite>>>%@", favourite);
    
    if (appDelegate.selectedObj != nil) {
        [(appDelegate.arrScrollViewCount)[0] setObject:[NSString stringWithFormat:@"%d",sender.isSelected ? 0 : 1] forKey:@"isFavourite"];
    } else {
        [(appDelegate.mutArrPlayVideo)[currentIndex] setObject:[NSString stringWithFormat:@"%d",sender.isSelected ? 0 : 1] forKey:@"isFavourite"];
    }
    
    NSString *favourite1 = [NSString stringWithFormat:@"%@",appDelegate.selectedObj != nil ? (appDelegate.arrScrollViewCount)[0][@"isFavourite"] : (appDelegate.mutArrPlayVideo)[currentIndex][@"isFavourite"]];
    
    NSLog(@"favourite1>>>%@", favourite1);
    
    [UIView transitionWithView:sender
                      duration:0.3
                       options:UIViewAnimationOptionTransitionCrossDissolve
                    animations:^{
        [sender setSelected:![sender isSelected]];
    } completion:nil];
}

#pragma mark - FlipImage Image
- (void)btnFlipImgClick:(UIButton *)sender {
    
    [UIView transitionWithView:sender
                      duration:0.3
                       options:UIViewAnimationOptionTransitionCrossDissolve
                    animations:^{
        [sender setSelected:![sender isSelected]];
    } completion:nil];
    
    NSString *mediaType = AppDelegateobj.mutArrPlayVideo[sender.tag][@"fileType"];
    
    if ([mediaType isEqualToString:@"MOVIE"]) {
        NSURL *inputURL = [NSURL fileURLWithPath:selectedVideoToPlayOnFullScreen];
        
        if ([sender isSelected]) {
            [self flipVideoHorizontallyAtURL:inputURL completion:^(NSURL *outputURL, NSError *error) {
                if (outputURL) {
                    [self setupPlayerWithURL:outputURL];
                } else {
                    NSLog(@"Failed to flip video: %@", error.localizedDescription);
                }
            }];
        } else {
            [self setupPlayerWithURL:inputURL];
        }
    }
}

// Add this new method to the class:
- (void)setupPlayerWithURL:(NSURL *)inputURL {
    AVPlayerItem *videoItem = [AVPlayerItem playerItemWithURL:inputURL];
    [self getVideoMediaTimeScale:inputURL];
    
    self.m_queueplayer = [AVQueuePlayer queuePlayerWithItems:@[videoItem]];
    (self.m_playerView).player = self.m_queueplayer;
    dispatch_time_t delay = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC));
    dispatch_after(delay, dispatch_get_main_queue(), ^{
        self->isanimationFinished = YES;
        self->isPlaying = NO;
//        [self play:nil];
        [self setPlayPauseImageForNormalState];
    });
}

- (void)flipVideoHorizontallyAtURL:(NSURL *)inputURL
                        completion:(void (^)(NSURL *outputURL, NSError *error))completion {
    
    AVAsset *asset = [AVAsset assetWithURL:inputURL];
    
    AVMutableComposition *composition = [AVMutableComposition composition];
    AVMutableCompositionTrack *videoTrack = [composition addMutableTrackWithMediaType:AVMediaTypeVideo
                                                                     preferredTrackID:kCMPersistentTrackID_Invalid];
    
    AVAssetTrack *sourceVideoTrack = [[asset tracksWithMediaType:AVMediaTypeVideo] firstObject];
    NSError *error = nil;
    [videoTrack insertTimeRange:CMTimeRangeMake(kCMTimeZero, asset.duration)
                        ofTrack:sourceVideoTrack
                         atTime:kCMTimeZero
                          error:&error];
    
    if (error) {
        completion(nil, error);
        return;
    }
    
    AVMutableVideoCompositionLayerInstruction *layerInstruction = [AVMutableVideoCompositionLayerInstruction videoCompositionLayerInstructionWithAssetTrack:videoTrack];
    
    CGAffineTransform transform = sourceVideoTrack.preferredTransform;
    CGFloat videoWidth = sourceVideoTrack.naturalSize.width;
    
    transform = CGAffineTransformConcat(transform, CGAffineTransformMakeScale(-1.0, 1.0));
    transform = CGAffineTransformConcat(transform, CGAffineTransformMakeTranslation(videoWidth, 0));
    
    [layerInstruction setTransform:transform atTime:kCMTimeZero];
    
    AVMutableVideoCompositionInstruction *instruction = [AVMutableVideoCompositionInstruction videoCompositionInstruction];
    instruction.timeRange = CMTimeRangeMake(kCMTimeZero, asset.duration);
    instruction.layerInstructions = @[layerInstruction];
    
    AVMutableVideoComposition *videoComposition = [AVMutableVideoComposition videoComposition];
    videoComposition.instructions = @[instruction];
    videoComposition.frameDuration = CMTimeMake(1, 30);
    videoComposition.renderSize = sourceVideoTrack.naturalSize;
    
    // Generate temp output URL
    NSString *outputPath = [NSTemporaryDirectory() stringByAppendingPathComponent:@"flipped_video.mov"];
    NSURL *outputURL = [NSURL fileURLWithPath:outputPath];
    
    // Remove old file if exists
    [[NSFileManager defaultManager] removeItemAtURL:outputURL error:nil];
    
    AVAssetExportSession *exportSession = [[AVAssetExportSession alloc] initWithAsset:composition
                                                                           presetName:AVAssetExportPresetHighestQuality];
    exportSession.outputURL = outputURL;
    exportSession.outputFileType = AVFileTypeQuickTimeMovie;
    exportSession.videoComposition = videoComposition;
    exportSession.shouldOptimizeForNetworkUse = YES;
    
    [exportSession exportAsynchronouslyWithCompletionHandler:^{
        if (exportSession.status == AVAssetExportSessionStatusCompleted) {
            completion(outputURL, nil);
        } else {
            completion(nil, exportSession.error);
        }
    }];
}

@end
